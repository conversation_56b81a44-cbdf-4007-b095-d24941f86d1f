# MongoDB协议PCAP生成工具

这个工具使用Python的Scapy库生成各种MongoDB协议场景的PCAP文件，用于测试流量解析器。

## 依赖

- Python 3.6+
- Scapy库
- Pymongo库
- python-snappy库 (可选，用于生成snappy压缩场景)
- zstandard库 (可选，用于生成zstd压缩场景)

## 安装依赖

```bash
# 核心依赖
pip3 install scapy pymongo

# 可选依赖 (用于生成所有压缩场景)
pip3 install "python-snappy>=0.6.0" "zstandard>=0.15.0"
```

## 支持的场景

- OP_COMPRESSED: 使用zlib, snappy, zstd压缩的MongoDB消息
- OP_QUERY: 标准的MongoDB查询消息，包括完整的请求和响应
- OP_INSERT: MongoDB插入操作消息，包含多个文档的插入和响应
- OP_UPDATE: MongoDB更新操作消息，包含选择器和更新操作符
- OP_DELETE: MongoDB删除操作消息，包含选择器和删除确认
- OP_MSG: MongoDB 3.6+的现代协议消息，使用命令文档格式
- OP_MSG with document_sequence: OP_MSG的高级格式，使用多Section分离命令和数据
- OP_GET_MORE: MongoDB获取更多查询结果的消息，用于处理大数据集的分页
- OP_KILL_CURSORS: MongoDB清理游标资源的消息

## 使用方法

项目包含两个脚本：
- `generate_mongodb_pcap.py`: 原始版本，手动构造BSON。
- `generate_mongodb_pcap_refactored.py`: **推荐使用**，依赖 `pymongo` 库，代码更健壮。

```bash
# 运行重构后的脚本
python3 generate_mongodb_pcap_refactored.py
```

生成的PCAP文件将保存在`mongodb_pcaps`目录中，包括：
- `mongodb_op_compressed.pcap`: OP_COMPRESSED消息场景
- `mongodb_op_query.pcap`: OP_QUERY消息场景，包含查询请求和回复响应
- `mongodb_op_compressed_zlib.pcap`: OP_COMPRESSED (zlib) 消息场景
- `mongodb_op_compressed_snappy.pcap`: OP_COMPRESSED (snappy) 消息场景
- `mongodb_op_compressed_zstd.pcap`: OP_COMPRESSED (zstd) 消息场景
- `mongodb_op_insert.pcap`: OP_INSERT消息场景，包含插入请求和确认响应
- `mongodb_op_update.pcap`: OP_UPDATE消息场景，包含更新请求和确认响应
- `mongodb_op_delete.pcap`: OP_DELETE消息场景，包含删除请求和确认响应
- `mongodb_op_msg.pcap`: OP_MSG消息场景，包含现代命令格式的查询请求和响应
- `mongodb_op_msg_document_sequence.pcap`: OP_MSG with document_sequence场景，使用多Section格式的批量插入操作
- `mongodb_op_get_more.pcap`: OP_GET_MORE消息场景，用于大数据集的分页查询
- `mongodb_paged_query_kill_cursors.pcap`: 包含OP_QUERY, OP_GET_MORE和OP_KILL_CURSORS的完整分页查询场景

## 消息格式说明

### OP_GET_MORE场景
- 客户端发送OP_GET_MORE消息，使用上次查询返回的cursorID请求更多数据
- 服务器返回OP_REPLY消息，包含后续的文档数据
- 演示了MongoDB处理大数据集时的分页查询机制
- 完整的TCP握手和挥手过程

### Paged Query with OP_KILL_CURSORS场景
- 客户端首先发送OP_QUERY，服务器返回第一批数据和一个活动的cursorID。
- 客户端接着发送OP_GET_MORE获取后续数据，服务器返回最后一批数据并将cursorID置为0。
- 客户端最后发送OP_KILL_CURSORS消息，清理服务器上的游标资源。
- 这是一个完整的、覆盖查询、分页和资源清理的真实业务流程。
- 完整的TCP握手和挥手过程

### OP_QUERY场景
- 客户端发送OP_QUERY消息，查询test.inventory集合中status="A"的文档
- 服务器返回OP_REPLY消息，包含匹配的文档数据
- 完整的TCP握手和挥手过程

### OP_INSERT场景
- 客户端发送OP_INSERT消息，向test.inventory集合插入两个文档
- 文档包含item、qty和tags字段，tags为数组类型
- 服务器返回OP_REPLY消息，包含插入确认信息（ok: 1, n: 2）
- 完整的TCP握手和挥手过程

### OP_UPDATE场景
- 客户端发送OP_UPDATE消息，更新test.inventory集合中item="journal"的文档
- 使用$inc操作符将qty字段增加10
- 服务器返回OP_REPLY消息，包含更新确认信息（ok: 1, n: 1, nModified: 1）
- 完整的TCP握手和挥手过程

### OP_DELETE场景
- 客户端发送OP_DELETE消息，删除test.inventory集合中status="D"的文档
- flags设置为0，表示只删除第一条匹配的文档
- 服务器返回OP_REPLY消息，包含删除确认信息（ok: 1, n: 1）
- 完整的TCP握手和挥手过程

### OP_MSG场景
- 客户端发送OP_MSG消息，使用现代命令格式查询test.inventory集合
- 命令文档包含find、filter、projection、batchSize和$db字段
- 查询条件为status="A"，投影字段为item，批量大小为2
- 服务器返回OP_MSG响应，包含cursor对象和查询结果
- cursor包含id、ns和firstBatch数组，返回匹配的文档
- 完整的TCP握手和挥手过程

### OP_MSG with document_sequence场景
- 客户端发送OP_MSG消息，使用多Section格式进行批量插入操作
- Section 0 (Kind 0): 包含命令文档，指定insert、$db和ordered字段
- Section 1 (Kind 1): 包含document_sequence，使用"documents"标识符
- 插入两个文档到test.inventory集合，包含item、qty和tags字段
- 服务器返回OP_MSG响应，包含插入确认信息（ok: 1, n: 2）
- 演示了OP_MSG协议的高级特性，适用于批量操作和大数据传输
- 完整的TCP握手和挥手过程

### OP_COMPRESSED场景  
- 支持zlib, snappy, zstd三种压缩算法压缩MongoDB消息
- 包含压缩头信息和压缩后的数据
- 支持原始消息的解压缩和解析

## 重要说明

### PCAP文件质量保证

所有生成的PCAP文件都严格按照MongoDB Wire Protocol官方规范实现：

**OP_MSG with document_sequence场景**：
- 正确实现了document_sequence的结构：**size + identifier + documents**
- Size字段包含identifier和documents的总大小（144字节）
- 完全符合MongoDB 3.6+的OP_MSG协议规范
- 可以被标准MongoDB客户端和服务器正确解析

**验证方法**：
- 我们生成的PCAP文件完全符合MongoDB Wire Protocol规范
- document_sequence的结构完全正确：size + identifier + documents
- 所有BSON文档的大小计算和编码都是准确的

**技术特点**：
- 精确的BSON文档大小计算和编码
- 正确的小端序字节序处理
- 完整的TCP握手和挥手过程
- 符合协议规范的消息结构和字段顺序