#!/usr/bin/env python3
"""
调试PostgreSQL消息解析的详细脚本
"""

import struct
import sys
from scapy.all import rdpcap, TCP

def analyze_postgresql_messages(pcap_file):
    """详细分析PostgreSQL消息"""
    print(f"=== 分析文件: {pcap_file} ===")
    
    try:
        packets = rdpcap(pcap_file)
    except Exception as e:
        print(f"读取pcap文件失败: {e}")
        return
    
    tcp_packets = [pkt for pkt in packets if TCP in pkt]
    print(f"找到 {len(tcp_packets)} 个TCP数据包")
    
    for i, pkt in enumerate(tcp_packets):
        if hasattr(pkt[TCP], 'payload') and len(pkt[TCP].payload) > 0:
            payload = bytes(pkt[TCP].payload)
            print(f"\n--- 数据包 {i+1} (长度: {len(payload)}) ---")
            print(f"十六进制: {payload[:100].hex()}")
            
            # 解析PostgreSQL消息
            offset = 0
            msg_count = 0
            
            while offset < len(payload):
                if offset + 4 > len(payload):
                    print(f"剩余数据不足4字节: {payload[offset:].hex()}")
                    break
                
                # 检查是否是启动消息
                if offset == 0:
                    potential_length = struct.unpack("!I", payload[offset:offset+4])[0]
                    if len(payload) >= 8:
                        potential_version = struct.unpack("!I", payload[offset+4:offset+8])[0]
                        if potential_version == 196608 and potential_length > 8 and potential_length <= len(payload):
                            print(f"  消息 {msg_count + 1}: 启动消息, 长度={potential_length}")
                            offset += potential_length
                            msg_count += 1
                            continue
                
                # 普通PostgreSQL消息
                if offset + 5 > len(payload):
                    print(f"剩余数据不足5字节: {payload[offset:].hex()}")
                    break
                
                msg_type = chr(payload[offset])
                msg_length = struct.unpack("!I", payload[offset+1:offset+5])[0]
                
                print(f"  消息 {msg_count + 1}: 类型='{msg_type}', 声明长度={msg_length}")
                
                # 检查长度是否合理
                if msg_length < 4:
                    print(f"    ❌ 长度太小: {msg_length}")
                    break
                elif msg_length > 1000000:  # 1MB限制
                    print(f"    ❌ 长度过大: {msg_length}")
                    break
                elif offset + 1 + msg_length > len(payload):
                    print(f"    ❌ 声明长度超出剩余数据")
                    print(f"    需要: {1 + msg_length}, 剩余: {len(payload) - offset}")
                    print(f"    剩余数据: {payload[offset:offset+20].hex()}")
                    break
                else:
                    print(f"    ✅ 长度正确")
                    # 显示消息内容的前几个字节
                    content = payload[offset+5:offset+1+msg_length]
                    if len(content) > 0:
                        print(f"    内容前16字节: {content[:16].hex()}")
                
                offset += 1 + msg_length
                msg_count += 1
                
                if msg_count > 20:  # 防止无限循环
                    print("    ⚠️ 消息数量过多，停止解析")
                    break
            
            print(f"总共解析了 {msg_count} 个消息")

def main():
    if len(sys.argv) != 2:
        print("用法: python3 debug_message_parsing.py <pcap文件>")
        sys.exit(1)
    
    pcap_file = sys.argv[1]
    analyze_postgresql_messages(pcap_file)

if __name__ == "__main__":
    main()
