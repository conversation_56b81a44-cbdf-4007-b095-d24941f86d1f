#!/usr/bin/env python3
"""
验证所有PostgreSQL PCAP文件的协议格式
"""

import os
import sys
import subprocess
import glob

def validate_all_pcaps():
    """验证所有生成的PCAP文件"""
    print("PostgreSQL PCAP文件全面验证工具")
    print("=" * 60)
    
    # 获取所有pcap文件
    normal_pcaps = glob.glob("pgsql_pcaps/*.pcap")
    abnormal_pcaps = glob.glob("pgsql_abnormal_pcaps/*.pcap")
    
    all_pcaps = sorted(normal_pcaps + abnormal_pcaps)
    
    if not all_pcaps:
        print("❌ 未找到任何PCAP文件")
        return False
    
    print(f"找到 {len(all_pcaps)} 个PCAP文件")
    print(f"  - 正常场景: {len(normal_pcaps)} 个")
    print(f"  - 异常场景: {len(abnormal_pcaps)} 个")
    print()
    
    # 分批验证文件（避免命令行过长）
    batch_size = 10
    total_valid = 0
    total_invalid = 0
    failed_files = []
    
    for i in range(0, len(all_pcaps), batch_size):
        batch = all_pcaps[i:i+batch_size]
        print(f"验证批次 {i//batch_size + 1}: {len(batch)} 个文件")
        
        try:
            # 调用验证脚本
            cmd = ["python3", "pgsql/test_pcap_validation.py"] + batch
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                # 解析输出统计信息
                output = result.stdout
                if "✓ 所有测试文件的PostgreSQL协议格式都正确！" in output:
                    print(f"  ✅ 批次验证通过")
                    total_valid += len(batch)
                else:
                    print(f"  ⚠️  批次部分通过")
                    # 解析具体的失败文件
                    lines = output.split('\n')
                    for line in lines:
                        if "✗ 发现协议格式问题" in line:
                            failed_files.extend(batch)
                            total_invalid += len(batch)
                            break
                    else:
                        total_valid += len(batch)
            else:
                print(f"  ❌ 批次验证失败: {result.stderr}")
                failed_files.extend(batch)
                total_invalid += len(batch)
                
        except subprocess.TimeoutExpired:
            print(f"  ⏰ 批次验证超时")
            failed_files.extend(batch)
            total_invalid += len(batch)
        except Exception as e:
            print(f"  ❌ 批次验证异常: {e}")
            failed_files.extend(batch)
            total_invalid += len(batch)
    
    print()
    print("=" * 60)
    print("验证结果汇总:")
    print(f"  ✅ 通过验证: {total_valid} 个文件")
    print(f"  ❌ 验证失败: {total_invalid} 个文件")
    print(f"  📊 成功率: {total_valid/(total_valid+total_invalid)*100:.1f}%")
    
    if failed_files:
        print()
        print("失败的文件:")
        for file in failed_files:
            print(f"  - {file}")
        print()
        print("建议:")
        print("  1. 检查失败文件的具体错误信息")
        print("  2. 使用单独验证命令详细分析:")
        print("     python3 pgsql/test_pcap_validation.py <失败的文件>")
        return False
    else:
        print()
        print("🎉 所有PCAP文件都通过了PostgreSQL协议格式验证！")
        print()
        print("验证内容包括:")
        print("  ✓ 消息类型字节正确")
        print("  ✓ 消息长度字段准确")
        print("  ✓ 消息边界对齐")
        print("  ✓ 启动消息格式")
        print("  ✓ 各种PostgreSQL消息类型")
        print()
        print("这些PCAP文件现在可以被Wireshark正确识别为PostgreSQL协议流量。")
        return True

if __name__ == "__main__":
    success = validate_all_pcaps()
    sys.exit(0 if success else 1)
