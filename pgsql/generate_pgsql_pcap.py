#!/usr/bin/env python3
from scapy.all import *
from scapy.layers.inet import IP, TCP
import struct
import random
import os

# PostgreSQL 默认端口
PGSQL_PORT = 5432

# 客户端和服务器IP地址
CLIENT_IP = "************"
SERVER_IP = "************"

# 随机端口和序列号
client_port = random.randint(40000, 65000)
seq_num = random.randint(1000000, 9000000)
ack_num = random.randint(1000000, 9000000)

def create_pgsql_packet(src, dst, sport, dport, seq, ack, payload, flags="PA"):
    """创建一个PostgreSQL数据包"""
    eth = Ether(type=0x0800)  # IPv4类型
    ip = IP(src=src, dst=dst)
    tcp = TCP(sport=sport, dport=dport, seq=seq, ack=ack, flags=flags)
    return eth/ip/tcp/payload

def create_tcp_handshake(src, dst, sport, dport):
    """创建TCP三次握手数据包"""
    packets = []
    # 初始序列号
    client_isn = random.randint(1000000, 9000000)
    server_isn = random.randint(1000000, 9000000)
    
    # SYN
    syn = create_pgsql_packet(src, dst, sport, dport, client_isn, 0, b"", flags="S")
    packets.append(syn)
    
    # SYN-ACK
    syn_ack = create_pgsql_packet(dst, src, dport, sport, server_isn, client_isn + 1, b"", flags="SA")
    packets.append(syn_ack)
    
    # ACK
    ack = create_pgsql_packet(src, dst, sport, dport, client_isn + 1, server_isn + 1, b"", flags="A")
    packets.append(ack)
    
    return packets, client_isn + 1, server_isn + 1

def create_tcp_teardown(src, dst, sport, dport, seq, ack):
    """创建TCP四次挥手数据包"""
    packets = []
    
    # FIN-ACK (client)
    fin_ack1 = create_pgsql_packet(src, dst, sport, dport, seq, ack, b"", flags="FA")
    packets.append(fin_ack1)
    
    # ACK (server)
    ack1 = create_pgsql_packet(dst, src, dport, sport, ack, seq + 1, b"", flags="A")
    packets.append(ack1)
    
    # FIN-ACK (server)
    fin_ack2 = create_pgsql_packet(dst, src, dport, sport, ack, seq + 1, b"", flags="FA")
    packets.append(fin_ack2)
    
    # ACK (client)
    ack2 = create_pgsql_packet(src, dst, sport, dport, seq + 1, ack + 1, b"", flags="A")
    packets.append(ack2)
    
    return packets

def write_pcap(packets, filename, is_abnormal=False):
    """将数据包写入PCAP文件"""
    if is_abnormal:
        target_dir = abnormal_output_dir
    else:
        target_dir = output_dir
    wrpcap(os.path.join(target_dir, filename), packets)
    print(f"已生成 {filename}")

def generate_auth_scenario():
    """生成认证场景的数据包"""
    packets = []
    global seq_num, ack_num
    
    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)
    
    # 1. 客户端发送启动消息
    startup_data = b"user\0postgres\0database\0testdb\0\0"
    startup_msg = struct.pack("!II", len(startup_data) + 8, 196608) + startup_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, startup_msg)
    packets.append(c_packet)
    seq_num += len(startup_msg)
    
    # 2. 服务器要求密码认证 (AuthenticationMD5Password)
    auth_req = b"R" + struct.pack("!II", 12, 5) + b"salt"
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, auth_req)
    packets.append(s_packet)
    ack_num += len(auth_req)
    
    # 3. 客户端发送密码响应
    password_data = b"md50123456789abcdef0123456789abcdef"
    password_resp = b"p" + struct.pack("!I", len(password_data) + 5) + password_data + b"\0"
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, password_resp)
    packets.append(c_packet)
    seq_num += len(password_resp)
    
    # 4. 服务器认证成功
    auth_ok_content = struct.pack("!I", 0)  # 认证成功标识
    auth_ok = b"R" + struct.pack("!I", len(auth_ok_content) + 4) + auth_ok_content
    # 参数状态 - 添加多个必要的参数
    param_messages = [
        (b"server_version", b"14.9"),
        (b"client_encoding", b"UTF8"),
        (b"DateStyle", b"ISO, MDY"),
        (b"TimeZone", b"UTC")
    ]
    params = b""
    for name, value in param_messages:
        param_data = name + b"\0" + value + b"\0"
        params += b"S" + struct.pack("!I", len(param_data) + 4) + param_data
    # 后端密钥数据
    backend_key_content = struct.pack("!II", 12345, 67890)
    backend_key = b"K" + struct.pack("!I", len(backend_key_content) + 4) + backend_key_content
    # 准备就绪状态
    ready_content = b"I"  # 空闲状态
    ready = b"Z" + struct.pack("!I", len(ready_content) + 4) + ready_content
    
    server_resp = auth_ok + params + backend_key + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp)
    packets.append(s_packet)
    ack_num += len(server_resp)
    
    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)
    
    write_pcap(packets, "pgsql_authentication.pcap")

def generate_simple_query_scenario():
    """生成简单查询场景的数据包"""
    packets = []
    global seq_num, ack_num
    
    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)
    
    # 1. 客户端发送查询
    query_str = b"SELECT * FROM users WHERE id = 1;\0"
    query = b"Q" + struct.pack("!I", len(query_str) + 4) + query_str
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, query)
    packets.append(c_packet)
    seq_num += len(query)
    
    # 2. 服务器响应行描述
    row_desc_content = struct.pack("!H", 3)  # 3列
    # id列
    row_desc_content += b"id\0" + struct.pack("!IHIHIHH", 0, 0, 23, 4, 65535, 0, 0)
    # name列
    row_desc_content += b"name\0" + struct.pack("!IHIHIHH", 0, 0, 25, 100, 100, 0, 0)
    # email列
    row_desc_content += b"email\0" + struct.pack("!IHIHIHH", 0, 0, 25, 100, 100, 0, 0)
    row_desc = b"T" + struct.pack("!I", len(row_desc_content) + 4) + row_desc_content
    
    # 数据行
    data_row_content = struct.pack("!H", 3)  # 3列
    # id值
    data_row_content += struct.pack("!I", 1) + b"1"
    # name值
    data_row_content += struct.pack("!I", 5) + b"Alice"
    # email值
    data_row_content += struct.pack("!I", 15) + b"<EMAIL>"
    data_row = b"D" + struct.pack("!I", len(data_row_content) + 4) + data_row_content
    
    # 命令完成
    cmd_complete_text = b"SELECT 1\0"
    cmd_complete = b"C" + struct.pack("!I", len(cmd_complete_text) + 4) + cmd_complete_text
    
    # 准备就绪
    ready = b"Z" + struct.pack("!IB", 5, 73)  # 73 = 'I' for idle
    
    server_resp = row_desc + data_row + cmd_complete + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp)
    packets.append(s_packet)
    ack_num += len(server_resp)
    
    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)
    
    write_pcap(packets, "pgsql_simple_query.pcap")

def generate_prepared_statement_scenario():
    """生成预处理语句场景的数据包"""
    packets = []
    global seq_num, ack_num
    
    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)
    
    # 1. 客户端发送Parse消息
    parse_data = b"stmt1\0" + b"SELECT * FROM users WHERE id = $1;\0" + struct.pack("!H", 1) + struct.pack("!I", 23)
    parse_msg = b"P" + struct.pack("!I", len(parse_data) + 4) + parse_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, parse_msg)
    packets.append(c_packet)
    seq_num += len(parse_msg)
    
    # 2. 客户端发送Bind消息
    bind_data = b"portal1\0" + b"stmt1\0" + struct.pack("!HHI", 1, 0, 1) + b"1" + struct.pack("!H", 0)
    bind_msg = b"B" + struct.pack("!I", len(bind_data) + 4) + bind_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, bind_msg)
    packets.append(c_packet)
    seq_num += len(bind_msg)
    
    # 3. 客户端发送Execute消息
    exec_data = b"portal1\0" + struct.pack("!I", 0)
    exec_msg = b"E" + struct.pack("!I", len(exec_data) + 4) + exec_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, exec_msg)
    packets.append(c_packet)
    seq_num += len(exec_msg)
    
    # 4. 客户端发送Sync消息
    sync_content = b""  # Sync消息无内容
    sync_msg = b"S" + struct.pack("!I", len(sync_content) + 4) + sync_content
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, sync_msg)
    packets.append(c_packet)
    seq_num += len(sync_msg)
    
    # 5. 服务器响应ParseComplete
    parse_complete_content = b""  # ParseComplete消息无内容
    parse_complete = b"1" + struct.pack("!I", len(parse_complete_content) + 4) + parse_complete_content

    # 6. 服务器响应BindComplete
    bind_complete_content = b""  # BindComplete消息无内容
    bind_complete = b"2" + struct.pack("!I", len(bind_complete_content) + 4) + bind_complete_content
    
    # 7. 服务器响应数据
    # 行描述 - 使用统一的有符号格式
    row_desc_content = struct.pack("!H", 3)  # 3列
    # id列
    row_desc_content += b"id\0" + struct.pack("!IHIhih", 0, 0, 23, 4, 4, 0)
    # name列 - 使用-1表示变长字段
    row_desc_content += b"name\0" + struct.pack("!IHIhih", 0, 0, 25, -1, -1, 0)
    # email列 - 使用-1表示变长字段
    row_desc_content += b"email\0" + struct.pack("!IHIhih", 0, 0, 25, -1, -1, 0)
    row_desc = b"T" + struct.pack("!I", len(row_desc_content) + 4) + row_desc_content

    # 数据行 - 动态计算字段长度
    data_row_content = struct.pack("!H", 3)  # 3列
    # id值
    id_value = b"1"
    data_row_content += struct.pack("!I", len(id_value)) + id_value
    # name值
    name_value = b"Alice"
    data_row_content += struct.pack("!I", len(name_value)) + name_value
    # email值 - 动态计算实际字节长度
    email_value = b"<EMAIL>"
    data_row_content += struct.pack("!I", len(email_value)) + email_value
    data_row = b"D" + struct.pack("!I", len(data_row_content) + 4) + data_row_content
    
    # 命令完成
    cmd_complete_text = b"SELECT 1\0"
    cmd_complete = b"C" + struct.pack("!I", len(cmd_complete_text) + 4) + cmd_complete_text
    
    # 准备就绪
    ready = b"Z" + struct.pack("!IB", 5, 73)  # 73 = 'I' for idle
    
    server_resp = parse_complete + bind_complete + row_desc + data_row + cmd_complete + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp)
    packets.append(s_packet)
    ack_num += len(server_resp)
    
    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)
    
    write_pcap(packets, "pgsql_prepared_statement.pcap")

def generate_error_scenario():
    """生成错误处理场景的数据包"""
    packets = []
    global seq_num, ack_num
    
    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)
    
    # 1. 客户端发送错误的查询
    query_str = b"SELECT * FROM non_existent_table;\0"
    query = b"Q" + struct.pack("!I", len(query_str) + 4) + query_str
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, query)
    packets.append(c_packet)
    seq_num += len(query)
    
    # 2. 服务器响应错误
    error_content = b"SERROR\0"  # 严重性
    error_content += b"C42P01\0"  # 代码
    error_content += b"MRelation \"non_existent_table\" does not exist\0"  # 消息
    error_content += b"Fparse_relation.c\0"  # 文件
    error_content += b"L1234\0"  # 行
    error_content += b"RparseRelation\0"  # 例程
    error_content += b"\0"  # 终止符
    error_resp = b"E" + struct.pack("!I", len(error_content) + 4) + error_content
    
    # 准备就绪
    ready = b"Z" + struct.pack("!IB", 5, 73)  # 73 = 'I' for idle
    
    server_resp = error_resp + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp)
    packets.append(s_packet)
    ack_num += len(server_resp)
    
    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)
    
    write_pcap(packets, "pgsql_error.pcap", is_abnormal=True)

def generate_multi_query_scenario():
    """生成多个simple query查询场景的数据包"""
    packets = []
    global seq_num, ack_num
    
    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)
    
    # 1. 构建查询字符串
    query_str = b"SELECT * FROM users WHERE id = 1; SELECT * FROM orders WHERE user_id = 1;\0"
    # 计算消息总长度：消息类型(1) + 长度字段(4) + 查询字符串长度
    query = b"Q" + struct.pack("!I", len(query_str) + 4) + query_str
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, query)
    packets.append(c_packet)
    seq_num += len(query)
    
    # 2. 构建第一个查询的响应数据
    # 行描述
    row_desc1_data = (
        b"id\0" + struct.pack("!IHIHIH", 0, 0, 23, 4, 4, 0) +
        b"name\0" + struct.pack("!IHIHIH", 0, 0, 25, 100, 100, 0) +
        b"email\0" + struct.pack("!IHIHIH", 0, 0, 25, 100, 100, 0)
    )
    row_desc1 = b"T" + struct.pack("!IH", len(row_desc1_data) + 6, 3) + row_desc1_data
    
    # 数据行
    data_row1_data = (
        struct.pack("!I", 1) + b"1" +
        struct.pack("!I", 5) + b"Alice" +
        struct.pack("!I", 14) + b"<EMAIL>"
    )
    data_row1 = b"D" + struct.pack("!IH", len(data_row1_data) + 6, 3) + data_row1_data
    
    # 命令完成
    cmd_complete1_data = b"SELECT 1\0"
    cmd_complete1 = b"C" + struct.pack("!I", len(cmd_complete1_data) + 4) + cmd_complete1_data
    
    # 3. 构建第二个查询的响应数据
    # 行描述
    row_desc2_data = (
        b"order_id\0" + struct.pack("!IHIHIH", 0, 0, 23, 4, 4, 0) +
        b"user_id\0" + struct.pack("!IHIHIH", 0, 0, 23, 4, 4, 0) +
        b"amount\0" + struct.pack("!IHIHIH", 0, 0, 701, 8, 8, 0) +
        b"status\0" + struct.pack("!IHIHIH", 0, 0, 25, 20, 20, 0)
    )
    row_desc2 = b"T" + struct.pack("!IH", len(row_desc2_data) + 6, 4) + row_desc2_data
    
    # 数据行1
    data_row2_1_data = (
        struct.pack("!I", 2) + b"10" +
        struct.pack("!I", 1) + b"1" +
        struct.pack("!I", 6) + b"100.50" +
        struct.pack("!I", 9) + b"completed"
    )
    data_row2_1 = b"D" + struct.pack("!IH", len(data_row2_1_data) + 6, 4) + data_row2_1_data
    
    # 数据行2
    data_row2_2_data = (
        struct.pack("!I", 2) + b"11" +
        struct.pack("!I", 1) + b"1" +
        struct.pack("!I", 5) + b"50.25" +
        struct.pack("!I", 7) + b"pending"
    )
    data_row2_2 = b"D" + struct.pack("!IH", len(data_row2_2_data) + 6, 4) + data_row2_2_data
    
    # 命令完成
    cmd_complete2_data = b"SELECT 2\0"
    cmd_complete2 = b"C" + struct.pack("!I", len(cmd_complete2_data) + 4) + cmd_complete2_data
    
    # 准备就绪
    ready = b"Z" + struct.pack("!IB", 5, 73)  # 73 = 'I' for idle
    
    server_resp = row_desc1 + data_row1 + cmd_complete1 + row_desc2 + data_row2_1 + data_row2_2 + cmd_complete2 + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp)
    packets.append(s_packet)
    ack_num += len(server_resp)
    
    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)
    
    write_pcap(packets, "pgsql_multi_query.pcap")

def generate_transaction_scenario():
    """生成事务场景的数据包"""
    packets = []
    global seq_num, ack_num
    
    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)
    
    # 1. 客户端发送BEGIN
    begin_str = b"BEGIN;\0"
    begin_query = b"Q" + struct.pack("!I", len(begin_str) + 4) + begin_str
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, begin_query)
    packets.append(c_packet)
    seq_num += len(begin_query)
    
    # 2. 服务器响应BEGIN
    cmd_str = b"BEGIN\0"
    cmd_complete = b"C" + struct.pack("!I", len(cmd_str) + 4) + cmd_str
    ready_content = struct.pack("!B", 84)  # 84 = 'T' for transaction
    ready = b"Z" + struct.pack("!I", len(ready_content) + 4) + ready_content
    server_resp = cmd_complete + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp)
    packets.append(s_packet)
    ack_num += len(server_resp)
    
    # 3. 客户端发送INSERT
    insert_str = b"INSERT INTO users (name, email) VALUES ('Bob', '<EMAIL>');\0"
    insert_query = b"Q" + struct.pack("!I", len(insert_str) + 4) + insert_str
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, insert_query)
    packets.append(c_packet)
    seq_num += len(insert_query)
    
    # 4. 服务器响应INSERT
    cmd_str = b"INSERT 0 1\0"
    cmd_complete = b"C" + struct.pack("!I", len(cmd_str) + 4) + cmd_str
    ready_content = struct.pack("!B", 84)  # 仍在事务中
    ready = b"Z" + struct.pack("!I", len(ready_content) + 4) + ready_content
    server_resp = cmd_complete + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp)
    packets.append(s_packet)
    ack_num += len(server_resp)
    
    # 5. 客户端发送COMMIT
    commit_str = b"COMMIT;\0"
    commit_query = b"Q" + struct.pack("!I", len(commit_str) + 4) + commit_str
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, commit_query)
    packets.append(c_packet)
    seq_num += len(commit_query)
    
    # 6. 服务器响应COMMIT
    cmd_str = b"COMMIT\0"
    cmd_complete = b"C" + struct.pack("!I", len(cmd_str) + 4) + cmd_str
    ready = b"Z" + struct.pack("!IB", 5, 73)  # 73 = 'I' for idle
    server_resp = cmd_complete + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp)
    packets.append(s_packet)
    ack_num += len(server_resp)
    
    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)
    
    write_pcap(packets, "pgsql_transaction.pcap")

def generate_auth_with_copy_scenario():
    """生成认证场景和COPY场景组合的数据包"""
    packets = []
    global seq_num, ack_num
    
    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)
    
    # 1. 客户端发送启动消息
    startup_data = b"user\0postgres\0database\0testdb\0\0"
    startup_msg = struct.pack("!II", len(startup_data) + 8, 196608) + startup_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, startup_msg)
    packets.append(c_packet)
    seq_num += len(startup_msg)
    
    # 2. 服务器要求密码认证 (AuthenticationMD5Password)
    auth_req = b"R" + struct.pack("!II", 12, 5) + b"salt"
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, auth_req)
    packets.append(s_packet)
    ack_num += len(auth_req)
    
    # 3. 客户端发送密码响应
    password_data = b"md50123456789abcdef0123456789abcdef"
    password_resp = b"p" + struct.pack("!I", len(password_data) + 5) + password_data + b"\0"
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, password_resp)
    packets.append(c_packet)
    seq_num += len(password_resp)
    
    # 4. 服务器认证成功
    auth_ok_content = struct.pack("!I", 0)  # 认证成功标识
    auth_ok = b"R" + struct.pack("!I", len(auth_ok_content) + 4) + auth_ok_content
    # 参数状态 - 添加多个必要的参数
    param_messages = [
        (b"server_version", b"14.9"),
        (b"client_encoding", b"UTF8"),
        (b"DateStyle", b"ISO, MDY"),
        (b"TimeZone", b"UTC")
    ]
    params = b""
    for name, value in param_messages:
        param_data = name + b"\0" + value + b"\0"
        params += b"S" + struct.pack("!I", len(param_data) + 4) + param_data
    # 后端密钥数据
    backend_key_content = struct.pack("!II", 12345, 67890)
    backend_key = b"K" + struct.pack("!I", len(backend_key_content) + 4) + backend_key_content
    # 准备就绪状态
    ready_content = b"I"  # 空闲状态
    ready = b"Z" + struct.pack("!I", len(ready_content) + 4) + ready_content
    
    server_resp = auth_ok + params + backend_key + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp)
    packets.append(s_packet)
    ack_num += len(server_resp)
    
    # 5. 客户端发送COPY命令
    copy_str = b"COPY users FROM STDIN WITH CSV HEADER;\0"
    copy_query = b"Q" + struct.pack("!I", len(copy_str) + 4) + copy_str
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, copy_query)
    packets.append(c_packet)
    seq_num += len(copy_query)
    
    # 6. 服务器响应COPY命令，准备接收数据
    copy_in_data = struct.pack("!B", 0) + struct.pack("!H", 3) + struct.pack("!HHH", 0, 0, 0)
    copy_in_response = b"G" + struct.pack("!I", len(copy_in_data) + 4) + copy_in_data
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, copy_in_response)
    packets.append(s_packet)
    ack_num += len(copy_in_response)
    
    # 7. 客户端发送数据行
    # 首先是标题行
    header_str = b"id,name,email\n"
    header_data = b"d" + struct.pack("!I", len(header_str) + 4) + header_str
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, header_data)
    packets.append(c_packet)
    seq_num += len(header_data)
    
    # 数据行1
    data_str1 = b"1,Charlie,<EMAIL>\n"
    data_row1 = b"d" + struct.pack("!I", len(data_str1) + 4) + data_str1
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, data_row1)
    packets.append(c_packet)
    seq_num += len(data_row1)
    
    # 数据行2
    data_str2 = b"2,David,<EMAIL>\n"
    data_row2 = b"d" + struct.pack("!I", len(data_str2) + 4) + data_str2
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, data_row2)
    packets.append(c_packet)
    seq_num += len(data_row2)
    
    # 8. 客户端发送结束标记
    copy_done_content = b""  # CopyDone消息无内容
    copy_done = b"c" + struct.pack("!I", len(copy_done_content) + 4) + copy_done_content
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, copy_done)
    packets.append(c_packet)
    seq_num += len(copy_done)
    
    # 9. 服务器响应完成
    cmd_tag = b"COPY 2\0"
    cmd_len = 4 + len(cmd_tag)
    cmd_complete = b"C" + struct.pack("!I", cmd_len) + cmd_tag
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, cmd_complete)
    packets.append(s_packet)
    ack_num += len(cmd_complete)
    
    # ReadyForQuery
    ready_content = b"I"  # 空闲状态
    ready = b"Z" + struct.pack("!I", len(ready_content) + 4) + ready_content
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, ready)
    packets.append(s_packet)
    ack_num += len(ready)
    
    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)
    
    write_pcap(packets, "pgsql_auth_with_copy.pcap")

def generate_notification_scenario():
    """生成通知场景的数据包"""
    packets = []
    global seq_num, ack_num
    
    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)
    
    # 1. 客户端发送LISTEN命令
    listen_str = b"LISTEN channel_test;\0"
    listen_query = b"Q" + struct.pack("!I", len(listen_str) + 4) + listen_str
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, listen_query)
    packets.append(c_packet)
    seq_num += len(listen_query)
    
    # 2. 服务器响应LISTEN
    cmd_str = b"LISTEN\0"
    cmd_complete = b"C" + struct.pack("!I", len(cmd_str) + 4) + cmd_str
    ready = b"Z" + struct.pack("!I", 5) + struct.pack("!B", 73)  # 73 = 'I' for idle
    server_resp = cmd_complete + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp)
    packets.append(s_packet)
    ack_num += len(server_resp)
    
    # 3. 客户端发送NOTIFY命令（在实际场景中可能是另一个客户端）
    notify_str = b"NOTIFY channel_test, 'This is a test';\0"
    notify_query = b"Q" + struct.pack("!I", len(notify_str) + 4) + notify_str
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, notify_query)
    packets.append(c_packet)
    seq_num += len(notify_query)
    
    # 4. 服务器响应NOTIFY
    cmd_str = b"NOTIFY\0"
    cmd_complete = b"C" + struct.pack("!I", len(cmd_str) + 4) + cmd_str
    ready = b"Z" + struct.pack("!I", 5) + struct.pack("!B", 73)  # 73 = 'I' for idle
    server_resp = cmd_complete + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp)
    packets.append(s_packet)
    ack_num += len(server_resp)
    
    # 5. 服务器发送异步通知
    channel = b"channel_test\0"
    payload = b"This is a test\0"
    notification = b"A" + struct.pack("!I", len(channel) + len(payload) + 12) + struct.pack("!II", 12345, 0) + channel + payload
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, notification)
    packets.append(s_packet)
    ack_num += len(notification)
    
    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)
    
    write_pcap(packets, "pgsql_notification.pcap")

def generate_large_result_scenario():
    """生成大结果集场景的数据包"""
    packets = []
    global seq_num, ack_num

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # 1. 客户端发送查询
    query_str = b"SELECT * FROM large_table;\0"
    query = b"Q" + struct.pack("!I", len(query_str) + 4) + query_str
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, query)
    packets.append(c_packet)
    seq_num += len(query)

    # 2. 服务器响应行描述
    # 动态计算RowDescription内容
    row_desc_content = struct.pack("!H", 2)  # 2列
    # id列 - 使用统一的有符号格式
    id_field_name = b"id\0"
    row_desc_content += id_field_name + struct.pack("!IHIhih", 0, 0, 23, 4, 4, 0)
    # data列 - 使用统一的有符号格式，-1表示变长字段
    data_field_name = b"data\0"
    row_desc_content += data_field_name + struct.pack("!IHIhih", 0, 0, 25, -1, -1, 0)

    # 动态计算RowDescription消息长度
    row_desc = b"T" + struct.pack("!I", len(row_desc_content) + 4) + row_desc_content

    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, row_desc)
    packets.append(s_packet)
    ack_num += len(row_desc)

    # 3. 服务器发送多个数据行
    for i in range(1, 11):  # 发送10行数据
        # 动态计算DataRow内容
        id_str = str(i).encode()
        data_str = f"This is row {i} with some sample data".encode()

        data_row_content = struct.pack("!H", 2)  # 2列
        # id值
        data_row_content += struct.pack("!I", len(id_str)) + id_str
        # data值
        data_row_content += struct.pack("!I", len(data_str)) + data_str

        # 动态计算DataRow消息长度
        data_row = b"D" + struct.pack("!I", len(data_row_content) + 4) + data_row_content

        s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, data_row)
        packets.append(s_packet)
        ack_num += len(data_row)
    
    # 4. 命令完成
    cmd_complete_text = b"SELECT 10\0"
    cmd_complete = b"C" + struct.pack("!I", len(cmd_complete_text) + 4) + cmd_complete_text
    
    # 5. 准备就绪
    ready = b"Z" + struct.pack("!IB", 5, 73)  # 73 = 'I' for idle
    
    server_resp = cmd_complete + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp)
    packets.append(s_packet)
    ack_num += len(server_resp)
    
    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)
    
    write_pcap(packets, "pgsql_large_result.pcap")

def generate_batch_operations_scenario():
    """生成批量操作场景的数据包"""
    packets = []
    global seq_num, ack_num
    
    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)
    
    # 1. 客户端发送BEGIN
    begin_str = b"BEGIN;\0"
    begin_query = b"Q" + struct.pack("!I", len(begin_str) + 4) + begin_str
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, begin_query)
    packets.append(c_packet)
    seq_num += len(begin_query)
    
    # 2. 服务器响应BEGIN
    cmd_str = b"BEGIN\0"
    cmd_complete = b"C" + struct.pack("!I", len(cmd_str) + 4) + cmd_str
    ready = b"Z" + struct.pack("!I", 5) + struct.pack("!B", 84)  # 84 = 'T' for transaction
    server_resp = cmd_complete + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp)
    packets.append(s_packet)
    ack_num += len(server_resp)
    
    # 3. 客户端发送多个INSERT
    for i in range(1, 4):  # 发送3个INSERT
        insert_str = f"INSERT INTO users (name, email) VALUES ('User{i}', 'user{i}@test.com');\0".encode()
        insert_query = b"Q" + struct.pack("!I", len(insert_str) + 4) + insert_str
        c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, insert_query)
        packets.append(c_packet)
        seq_num += len(insert_query)
        
        # 服务器响应每个INSERT
        cmd_str = b"INSERT 0 1\0"
        cmd_complete = b"C" + struct.pack("!I", len(cmd_str) + 4) + cmd_str
        ready = b"Z" + struct.pack("!I", 5) + struct.pack("!B", 84)  # 仍在事务中
        server_resp = cmd_complete + ready
        s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp)
        packets.append(s_packet)
        ack_num += len(server_resp)
    
    # 4. 客户端发送COMMIT
    commit_str = b"COMMIT;\0"
    commit_query = b"Q" + struct.pack("!I", len(commit_str) + 4) + commit_str
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, commit_query)
    packets.append(c_packet)
    seq_num += len(commit_query)
    
    # 5. 服务器响应COMMIT
    cmd_str = b"COMMIT\0"
    cmd_complete = b"C" + struct.pack("!I", len(cmd_str) + 4) + cmd_str
    ready = b"Z" + struct.pack("!IB", 5, 73)  # 73 = 'I' for idle
    server_resp = cmd_complete + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp)
    packets.append(s_packet)
    ack_num += len(server_resp)
    
    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)
    
    write_pcap(packets, "pgsql_batch_operations.pcap")

def generate_cancel_query_scenario():
    """生成取消查询场景的数据包"""
    packets = []
    global seq_num, ack_num
    
    # 添加TCP三次握手 - 主连接
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)
    
    # 1. 客户端发送长时间运行的查询
    query_str = b"SELECT * FROM large_table WHERE complex_condition;\0"
    query = b"Q" + struct.pack("!I", len(query_str) + 4) + query_str
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, query)
    packets.append(c_packet)
    seq_num += len(query)
    
    # 2. 客户端发送取消请求（通过另一个连接）
    # 为取消请求创建新的连接
    cancel_port = random.randint(40000, 65000)
    
    # 添加取消连接的TCP三次握手
    cancel_handshake, cancel_seq, cancel_ack = create_tcp_handshake(CLIENT_IP, SERVER_IP, cancel_port, PGSQL_PORT)
    packets.extend(cancel_handshake)
    
    # CancelRequest消息 - 正确格式
    # 消息总长度为16字节，包含:
    # - 消息长度(4字节): 固定值16
    # - 请求类型(4字节): 固定值80877102 (魔术数字)
    # - 进程ID(4字节): 使用网络字节序
    # - 密钥(4字节): 使用网络字节序
    cancel_msg = struct.pack("!IIII", 16, 80877102, 12345, 67890)
    
    # 发送取消请求 - 直接通过TCP发送原始数据，不使用PostgreSQL消息头
    eth = Ether(type=0x0800)  # IPv4类型
    ip = IP(src=CLIENT_IP, dst=SERVER_IP)
    tcp = TCP(sport=cancel_port, dport=PGSQL_PORT, seq=cancel_seq, ack=cancel_ack, flags="PA")
    cancel_packet = eth/ip/tcp/cancel_msg
    packets.append(cancel_packet)
    
    # 服务器确认收到取消请求并立即关闭连接
    ack_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, cancel_port, cancel_ack, cancel_seq + len(cancel_msg), b"", flags="A")
    packets.append(ack_packet)
    
    # 取消连接的TCP四次挥手
    cancel_teardown = create_tcp_teardown(CLIENT_IP, SERVER_IP, cancel_port, PGSQL_PORT, cancel_seq + len(cancel_msg), cancel_ack)
    packets.extend(cancel_teardown)
    
    # 3. 服务器向原始连接发送查询取消通知
    # 构建ErrorResponse消息
    # 计算消息总长度：4(长度字段) + 1('S') + 6("ERROR"+\0) + 1('C') + 6("57014"+\0) + 1('M') + 20("Query cancelled by user"+\0) + 1(终止符\0) = 40字节
    error_resp = b"E" + struct.pack("!I", 44)
    error_resp += b"S" + b"ERROR\0"  # 严重性字段
    error_resp += b"C" + b"57014\0"  # 错误代码字段
    error_resp += b"M" + b"Query cancelled by user\0"  # 错误消息字段
    error_resp += b"\0"  # 消息终止符
    
    # 准备就绪
    ready = b"Z" + struct.pack("!I", 5) + b"I"
    
    server_resp = error_resp + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp)
    packets.append(s_packet)
    ack_num += len(server_resp)
    
    # 添加主连接的TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)
    
    write_pcap(packets, "pgsql_cancel_query.pcap")

def generate_binary_extended_query_scenario():
    """生成返回二进制格式数据的扩展查询场景的数据包"""
    packets = []
    global seq_num, ack_num
    
    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)
    
    # 1. 客户端发送Parse消息
    parse_data = b"stmt1\0" + b"SELECT id, data FROM binary_table WHERE id = $1;\0" + struct.pack("!H", 1) + struct.pack("!I", 23)
    parse_msg = b"P" + struct.pack("!I", len(parse_data) + 4) + parse_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, parse_msg)
    packets.append(c_packet)
    seq_num += len(parse_msg)
    
    # 2. 客户端发送Describe消息(描述预处理语句)
    describe_data = b"S" + b"stmt1\0"
    describe_msg = b"D" + struct.pack("!I", len(describe_data) + 4) + describe_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, describe_msg)
    packets.append(c_packet)
    seq_num += len(describe_msg)
    
    # 3. 客户端发送Bind消息，指定使用二进制格式
    bind_data = b"portal1\0stmt1\0" + struct.pack("!H", 1) + struct.pack("!H", 0) + struct.pack("!H", 1) + struct.pack("!i", 3) + b"ams" + struct.pack("!H", 1) + struct.pack("!H", 1)
    bind_msg = b"B" + struct.pack("!I", len(bind_data) + 4) + bind_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, bind_msg)
    packets.append(c_packet)
    seq_num += len(bind_msg)
    
    # 4. 客户端发送Execute消息
    execute_data = b"portal1\0" + struct.pack("!I", 0)
    execute_msg = b"E" + struct.pack("!I", len(execute_data) + 4) + execute_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, execute_msg)
    packets.append(c_packet)
    seq_num += len(execute_msg)
    
    # 5. 客户端发送Sync消息
    sync_msg = b"S" + struct.pack("!I", 4)
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, sync_msg)
    packets.append(c_packet)
    seq_num += len(sync_msg)
    
    # 6. 服务器响应ParseComplete
    parse_complete = b"1" + struct.pack("!I", 4)
    
    # 7. 服务器响应ParameterDescription
    param_desc = b"t" + struct.pack("!I", 10) + struct.pack("!H", 1) + struct.pack("!I", 23)
    
    # 8. 服务器响应RowDescription(二进制格式)
    row_desc_data = struct.pack("!H", 2)  # 2列
    # id列(int4类型)
    row_desc_data += b"id\0" + struct.pack("!ihihih", 0, 0, 23, -1, -1, 1)
    # data列(bytea类型)
    row_desc_data += b"data\0" + struct.pack("!ihihih", 0, 0, 17, -1, -1, 1)
    row_desc = b"T" + struct.pack("!I", len(row_desc_data) + 4) + row_desc_data
    
    # 9. 服务器响应BindComplete
    bind_complete = b"2" + struct.pack("!I", 4)
    
    # 10. 服务器发送二进制格式的数据行
    data_row_content = struct.pack("!H", 2)  # 2列
    # id值(4字节整数，二进制格式)
    data_row_content += struct.pack("!I", 4) + struct.pack("!i", 42)  # 修改第一个为I表示长度
    # data值(二进制数据，bytea格式)
    binary_data = bytes([0x01, 0x02, 0x03, 0x04, 0x05])
    data_row_content += struct.pack("!I", len(binary_data)) + binary_data  # 修改为I表示长度
    data_row = b"D" + struct.pack("!I", len(data_row_content) + 4) + data_row_content
    
    # 11. 命令完成标记
    complete_data = b"SELECT 1\0"
    command_complete = b"C" + struct.pack("!I", len(complete_data) + 4) + complete_data
    
    # 12. 准备就绪
    ready = b"Z" + struct.pack("!I", 5) + b"I"
    
    # 组合所有服务器响应
    server_resp = parse_complete + param_desc + row_desc + bind_complete + data_row + command_complete + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp)
    packets.append(s_packet)
    ack_num += len(server_resp)
    
    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)
    
    write_pcap(packets, "pgsql_binary_extended_query.pcap")

def generate_high_performance_scenario():
    """生成高性能测试场景的数据包 - 目标100k QPS"""
    packets = []
    global seq_num, ack_num

    # 为了达到100k QPS，我们需要生成大量的请求-响应对
    # 每个QPS包含1个请求和1个响应，目标报文大小约500字节

    print("开始生成高性能测试场景...")
    print("目标: 100k QPS, 报文大小: ~500字节")

    # 计算需要生成的数据量
    target_qps = 100000  # 100k QPS
    test_duration = 1  # 1秒的测试数据
    packets_needed = target_qps * 2 * test_duration  # 每个QPS包含请求+响应

    print(f"需要生成 {packets_needed} 个报文")
    print(f"预计文件大小: ~{packets_needed * 500 / 1024 / 1024:.1f} MB")

    # 添加TCP三次握手（只需要一次）
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # 生成大量的简单查询，每个查询约500字节
    batch_size = 1000  # 每批处理1000个查询
    total_batches = target_qps // batch_size

    for batch in range(total_batches):
        if batch % 10 == 0:  # 每10批显示一次进度
            print(f"处理批次 {batch + 1}/{total_batches} ({(batch + 1) / total_batches * 100:.1f}%)")

        for i in range(batch_size):
            query_id = batch * batch_size + i + 1

            # 构造约500字节的查询
            # 基础查询 + 填充数据达到目标大小
            base_query = f"SELECT id, name, email, created_at FROM users WHERE id = {query_id}"
            # 添加注释来达到目标字节数
            padding = "-- " + "x" * (450 - len(base_query))  # 填充到约500字节
            full_query = base_query + padding + ";\0"

            query_bytes = full_query.encode()
            query_msg = b"Q" + struct.pack("!I", len(query_bytes) + 4) + query_bytes

            # 客户端请求
            c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT,
                                         seq_num, ack_num, query_msg)
            packets.append(c_packet)
            seq_num += len(query_msg)

            # 构造约500字节的响应
            # 行描述
            row_desc_data = (
                b"id\0" + struct.pack("!IHIHIH", 0, 0, 23, 4, 4, 0) +
                b"name\0" + struct.pack("!IHIHIH", 0, 0, 25, 50, 50, 0) +
                b"email\0" + struct.pack("!IHIHIH", 0, 0, 25, 100, 100, 0) +
                b"created_at\0" + struct.pack("!IHIHIH", 0, 0, 1184, 8, 8, 0)
            )
            row_desc = b"T" + struct.pack("!IH", len(row_desc_data) + 6, 4) + row_desc_data

            # 数据行 - 填充到合适大小
            user_name = f"User{query_id:06d}"
            user_email = f"user{query_id:06d}@performance-test.com"
            timestamp_data = "2024-01-01 12:00:00"
            # 添加额外数据以达到目标大小
            padding_data = "x" * (200 - len(user_name) - len(user_email) - len(timestamp_data))

            data_row_content = (
                struct.pack("!I", len(str(query_id))) + str(query_id).encode() +
                struct.pack("!I", len(user_name)) + user_name.encode() +
                struct.pack("!I", len(user_email)) + user_email.encode() +
                struct.pack("!I", len(timestamp_data)) + timestamp_data.encode()
            )
            data_row = b"D" + struct.pack("!IH", len(data_row_content) + 6, 4) + data_row_content

            # 命令完成
            cmd_complete_data = b"SELECT 1\0"
            cmd_complete = b"C" + struct.pack("!I", len(cmd_complete_data) + 4) + cmd_complete_data

            # 准备就绪
            ready = b"Z" + struct.pack("!IB", 5, 73)

            # 组合服务器响应
            server_resp = row_desc + data_row + cmd_complete + ready
            s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port,
                                         ack_num, seq_num, server_resp)
            packets.append(s_packet)
            ack_num += len(server_resp)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    print(f"实际生成了 {len(packets)} 个数据包")
    write_pcap(packets, "pgsql_high_performance_100k_qps.pcap")
    print("高性能测试场景生成完成!")

def generate_cleartext_auth_scenario():
    """生成明文密码认证场景的数据包"""
    packets = []
    global seq_num, ack_num

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # 1. 客户端发送启动消息
    startup_data = b"user\0testuser\0database\0testdb\0\0"
    startup_msg = struct.pack("!II", len(startup_data) + 8, 196608) + startup_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, startup_msg)
    packets.append(c_packet)
    seq_num += len(startup_msg)

    # 2. 服务器要求明文密码认证 (AuthenticationCleartextPassword)
    auth_req = b"R" + struct.pack("!II", 8, 3)  # 类型3表示明文密码认证
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, auth_req)
    packets.append(s_packet)
    ack_num += len(auth_req)

    # 3. 客户端发送明文密码响应
    password_data = b"mypassword123"
    password_resp = b"p" + struct.pack("!I", len(password_data) + 5) + password_data + b"\0"
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, password_resp)
    packets.append(c_packet)
    seq_num += len(password_resp)

    # 4. 服务器认证成功
    auth_ok = b"R" + struct.pack("!I", 8) + struct.pack("!I", 0)
    # 参数状态
    param_messages = [
        (b"server_version", b"14.9"),
        (b"client_encoding", b"UTF8"),
        (b"DateStyle", b"ISO, MDY"),
        (b"TimeZone", b"UTC")
    ]
    params = b""
    for name, value in param_messages:
        param_data = name + b"\0" + value + b"\0"
        params += b"S" + struct.pack("!I", len(param_data) + 4) + param_data
    # 后端密钥数据
    backend_key = b"K" + struct.pack("!I", 12) + struct.pack("!II", 12345, 67890)
    # 准备就绪状态
    ready = b"Z" + struct.pack("!I", 5) + b"I"

    server_resp = auth_ok + params + backend_key + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp)
    packets.append(s_packet)
    ack_num += len(server_resp)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "pgsql_cleartext_authentication.pcap")

def generate_auth_failure_scenario():
    """生成认证失败场景的数据包 - 错误密码导致认证失败"""
    packets = []
    global seq_num, ack_num

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # 1. 客户端发送启动消息
    startup_data = b"user\0testuser\0database\0testdb\0\0"
    startup_msg = struct.pack("!II", len(startup_data) + 8, 196608) + startup_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, startup_msg)
    packets.append(c_packet)
    seq_num += len(startup_msg)

    # 2. 服务器要求MD5密码认证
    auth_req = b"R" + struct.pack("!II", 12, 5) + b"salt"
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, auth_req)
    packets.append(s_packet)
    ack_num += len(auth_req)

    # 3. 客户端发送错误的密码响应
    wrong_password_data = b"md5wrongpasswordhash0123456789abcdef"
    password_resp = b"p" + struct.pack("!I", len(wrong_password_data) + 5) + wrong_password_data + b"\0"
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, password_resp)
    packets.append(c_packet)
    seq_num += len(password_resp)

    # 4. 服务器返回认证失败错误
    error_content = b"SFATAL\0"  # 严重性：FATAL
    error_content += b"C28P01\0"  # 错误代码：invalid_password
    error_content += b"Mpassword authentication failed for user \"testuser\"\0"  # 错误消息
    error_content += b"Fauth.c\0"  # 文件名
    error_content += b"L123\0"  # 行号
    error_content += b"Rauth_failed\0"  # 例程名
    error_content += b"\0"  # 终止符
    error_resp = b"E" + struct.pack("!I", len(error_content) + 4) + error_content

    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, error_resp)
    packets.append(s_packet)
    ack_num += len(error_resp)

    # 5. 服务器关闭连接（认证失败后立即关闭）
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "pgsql_auth_failure.pcap", is_abnormal=True)

def generate_user_not_exist_scenario():
    """生成用户不存在认证失败场景的数据包"""
    packets = []
    global seq_num, ack_num

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # 1. 客户端发送启动消息（使用不存在的用户名）
    startup_data = b"user\0nonexistentuser\0database\0testdb\0\0"
    startup_msg = struct.pack("!II", len(startup_data) + 8, 196608) + startup_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, startup_msg)
    packets.append(c_packet)
    seq_num += len(startup_msg)

    # 2. 服务器直接返回用户不存在错误（无需认证步骤）
    error_content = b"SFATAL\0"  # 严重性：FATAL
    error_content += b"C28000\0"  # 错误代码：invalid_authorization_specification
    error_content += b"Mrole \"nonexistentuser\" does not exist\0"  # 错误消息
    error_content += b"Fpostinit.c\0"  # 文件名
    error_content += b"L456\0"  # 行号
    error_content += b"RInitializeSessionUserId\0"  # 例程名
    error_content += b"\0"  # 终止符
    error_resp = b"E" + struct.pack("!I", len(error_content) + 4) + error_content

    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, error_resp)
    packets.append(s_packet)
    ack_num += len(error_resp)

    # 3. 服务器关闭连接
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "pgsql_user_not_exist.pcap", is_abnormal=True)

def generate_sasl_auth_scenario():
    """生成SASL认证场景的数据包 - SCRAM-SHA-256认证流程"""
    packets = []
    global seq_num, ack_num

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # 1. 客户端发送启动消息
    startup_data = b"user\0sasluser\0database\0testdb\0\0"
    startup_msg = struct.pack("!II", len(startup_data) + 8, 196608) + startup_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, startup_msg)
    packets.append(c_packet)
    seq_num += len(startup_msg)

    # 2. 服务器发送SASL认证请求，支持SCRAM-SHA-256
    sasl_mechanisms = b"SCRAM-SHA-256\0SCRAM-SHA-256-PLUS\0\0"  # 支持的SASL机制列表
    auth_sasl = b"R" + struct.pack("!II", len(sasl_mechanisms) + 8, 10) + sasl_mechanisms
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, auth_sasl)
    packets.append(s_packet)
    ack_num += len(auth_sasl)

    # 3. 客户端发送SASL初始响应
    selected_mechanism = b"SCRAM-SHA-256\0"
    # SCRAM-SHA-256初始客户端消息：n,,n=user,r=clientnonce123456789
    initial_response = b"n,,n=sasluser,r=clientnonce123456789"
    sasl_initial = b"p" + struct.pack("!I", len(selected_mechanism) + 4 + len(initial_response) + 4)
    sasl_initial += selected_mechanism + struct.pack("!i", len(initial_response)) + initial_response
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, sasl_initial)
    packets.append(c_packet)
    seq_num += len(sasl_initial)

    # 4. 服务器发送SASL继续消息
    # 服务器第一次挑战：r=clientnonce123456789servernonce987654321,s=salt,i=4096
    server_first = b"r=clientnonce123456789servernonce987654321,s=c2FsdA==,i=4096"
    auth_sasl_continue = b"R" + struct.pack("!II", len(server_first) + 8, 11) + server_first
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, auth_sasl_continue)
    packets.append(s_packet)
    ack_num += len(auth_sasl_continue)

    # 5. 客户端发送SASL响应
    # 客户端最终消息：c=biws,r=clientnonce123456789servernonce987654321,p=proof
    client_final = b"c=biws,r=clientnonce123456789servernonce987654321,p=dHdAbVFCRkMwL0RVZEVOTzAxa2dMV0E9PQ=="
    sasl_response = b"p" + struct.pack("!I", len(client_final) + 4) + client_final
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, sasl_response)
    packets.append(c_packet)
    seq_num += len(sasl_response)

    # 6. 服务器发送SASL最终消息
    # 服务器验证：v=serverproof
    server_final = b"v=NlJEUjJWUzlEUkVGTlEwOUJSVUZCUVVGQlFVRkJRVUZCUVE9PQ=="
    auth_sasl_final = b"R" + struct.pack("!II", len(server_final) + 8, 12) + server_final
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, auth_sasl_final)
    packets.append(s_packet)
    ack_num += len(auth_sasl_final)

    # 7. 服务器发送认证成功
    auth_ok_content = struct.pack("!I", 0)  # 认证成功标识
    auth_ok = b"R" + struct.pack("!I", len(auth_ok_content) + 4) + auth_ok_content
    # 参数状态
    param_messages = [
        (b"server_version", b"14.9"),
        (b"client_encoding", b"UTF8"),
        (b"DateStyle", b"ISO, MDY"),
        (b"TimeZone", b"UTC")
    ]
    params = b""
    for name, value in param_messages:
        param_data = name + b"\0" + value + b"\0"
        params += b"S" + struct.pack("!I", len(param_data) + 4) + param_data
    # 后端密钥数据
    backend_key_content = struct.pack("!II", 54321, 98765)
    backend_key = b"K" + struct.pack("!I", len(backend_key_content) + 4) + backend_key_content
    # 准备就绪状态
    ready_content = b"I"  # 空闲状态
    ready = b"Z" + struct.pack("!I", len(ready_content) + 4) + ready_content

    server_resp = auth_ok + params + backend_key + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp)
    packets.append(s_packet)
    ack_num += len(server_resp)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "pgsql_sasl_authentication.pcap")

def generate_describe_statement_scenario():
    """生成Describe预处理语句场景的数据包"""
    packets = []
    global seq_num, ack_num

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # 1. 客户端发送Parse消息创建预处理语句
    parse_data = b"test_stmt\0" + b"SELECT id, name FROM users WHERE age > $1 AND city = $2;\0" + struct.pack("!H", 2) + struct.pack("!II", 23, 25)  # int4, text
    parse_msg = b"P" + struct.pack("!I", len(parse_data) + 4) + parse_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, parse_msg)
    packets.append(c_packet)
    seq_num += len(parse_msg)

    # 2. 客户端发送Describe消息描述预处理语句
    describe_data = b"S" + b"test_stmt\0"  # 'S'表示描述预处理语句
    describe_msg = b"D" + struct.pack("!I", len(describe_data) + 4) + describe_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, describe_msg)
    packets.append(c_packet)
    seq_num += len(describe_msg)

    # 3. 客户端发送Sync消息
    sync_msg = b"S" + struct.pack("!I", 4)
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, sync_msg)
    packets.append(c_packet)
    seq_num += len(sync_msg)

    # 4. 服务器响应ParseComplete
    parse_complete = b"1" + struct.pack("!I", 4)

    # 5. 服务器响应ParameterDescription（描述参数）
    param_desc_data = struct.pack("!H", 2) + struct.pack("!II", 23, 25)  # 2个参数：int4, text
    param_desc = b"t" + struct.pack("!I", len(param_desc_data) + 4) + param_desc_data

    # 6. 服务器响应RowDescription（描述返回的行）
    row_desc_data = struct.pack("!H", 2)  # 2列
    # id列
    row_desc_data += b"id\0" + struct.pack("!IHIHIH", 0, 0, 23, 4, 4, 0)
    # name列
    row_desc_data += b"name\0" + struct.pack("!IHIHIH", 0, 0, 25, 100, 100, 0)
    row_desc = b"T" + struct.pack("!I", len(row_desc_data) + 4) + row_desc_data

    # 7. 准备就绪
    ready = b"Z" + struct.pack("!IB", 5, 73)  # 73 = 'I' for idle

    server_resp = parse_complete + param_desc + row_desc + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp)
    packets.append(s_packet)
    ack_num += len(server_resp)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "pgsql_describe_statement.pcap")

def generate_describe_portal_scenario():
    """生成Describe门户场景的数据包"""
    packets = []
    global seq_num, ack_num

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # 1. 客户端发送Parse消息
    parse_data = b"stmt1\0" + b"SELECT * FROM products WHERE price > $1;\0" + struct.pack("!H", 1) + struct.pack("!I", 701)  # numeric
    parse_msg = b"P" + struct.pack("!I", len(parse_data) + 4) + parse_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, parse_msg)
    packets.append(c_packet)
    seq_num += len(parse_msg)

    # 2. 客户端发送Bind消息创建门户
    bind_data = b"test_portal\0stmt1\0" + struct.pack("!H", 1) + struct.pack("!H", 0) + struct.pack("!H", 1) + struct.pack("!I", 5) + b"100.0" + struct.pack("!H", 0)
    bind_msg = b"B" + struct.pack("!I", len(bind_data) + 4) + bind_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, bind_msg)
    packets.append(c_packet)
    seq_num += len(bind_msg)

    # 3. 客户端发送Describe消息描述门户
    describe_data = b"P" + b"test_portal\0"  # 'P'表示描述门户
    describe_msg = b"D" + struct.pack("!I", len(describe_data) + 4) + describe_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, describe_msg)
    packets.append(c_packet)
    seq_num += len(describe_msg)

    # 4. 客户端发送Sync消息
    sync_msg = b"S" + struct.pack("!I", 4)
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, sync_msg)
    packets.append(c_packet)
    seq_num += len(sync_msg)

    # 5. 服务器响应
    parse_complete = b"1" + struct.pack("!I", 4)
    bind_complete = b"2" + struct.pack("!I", 4)

    # 门户的行描述
    row_desc_data = struct.pack("!H", 3)  # 3列
    # id列
    row_desc_data += b"id\0" + struct.pack("!IHIHIH", 0, 0, 23, 4, 4, 0)
    # name列
    row_desc_data += b"name\0" + struct.pack("!IHIHIH", 0, 0, 25, 100, 100, 0)
    # price列
    row_desc_data += b"price\0" + struct.pack("!IHIHIH", 0, 0, 701, 8, 8, 0)
    row_desc = b"T" + struct.pack("!I", len(row_desc_data) + 4) + row_desc_data

    ready = b"Z" + struct.pack("!IB", 5, 73)

    server_resp = parse_complete + bind_complete + row_desc + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp)
    packets.append(s_packet)
    ack_num += len(server_resp)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "pgsql_describe_portal.pcap")

def generate_close_statement_portal_scenario():
    """生成Close预处理语句和门户场景的数据包"""
    packets = []
    global seq_num, ack_num

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # 1. 客户端发送Parse消息创建预处理语句
    parse_data = b"temp_stmt\0" + b"SELECT count(*) FROM users;\0" + struct.pack("!H", 0)  # 无参数
    parse_msg = b"P" + struct.pack("!I", len(parse_data) + 4) + parse_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, parse_msg)
    packets.append(c_packet)
    seq_num += len(parse_msg)

    # 2. 客户端发送Bind消息创建门户
    bind_data = b"temp_portal\0temp_stmt\0" + struct.pack("!H", 0) + struct.pack("!H", 0) + struct.pack("!H", 0)
    bind_msg = b"B" + struct.pack("!I", len(bind_data) + 4) + bind_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, bind_msg)
    packets.append(c_packet)
    seq_num += len(bind_msg)

    # 3. 客户端发送Close消息关闭门户
    close_portal_data = b"P" + b"temp_portal\0"  # 'P'表示关闭门户
    close_portal_msg = b"C" + struct.pack("!I", len(close_portal_data) + 4) + close_portal_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, close_portal_msg)
    packets.append(c_packet)
    seq_num += len(close_portal_msg)

    # 4. 客户端发送Close消息关闭预处理语句
    close_stmt_data = b"S" + b"temp_stmt\0"  # 'S'表示关闭预处理语句
    close_stmt_msg = b"C" + struct.pack("!I", len(close_stmt_data) + 4) + close_stmt_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, close_stmt_msg)
    packets.append(c_packet)
    seq_num += len(close_stmt_msg)

    # 5. 客户端发送Sync消息
    sync_msg = b"S" + struct.pack("!I", 4)
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, sync_msg)
    packets.append(c_packet)
    seq_num += len(sync_msg)

    # 6. 服务器响应
    parse_complete = b"1" + struct.pack("!I", 4)
    bind_complete = b"2" + struct.pack("!I", 4)
    close_complete1 = b"3" + struct.pack("!I", 4)  # 关闭门户完成
    close_complete2 = b"3" + struct.pack("!I", 4)  # 关闭预处理语句完成
    ready = b"Z" + struct.pack("!IB", 5, 73)

    server_resp = parse_complete + bind_complete + close_complete1 + close_complete2 + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp)
    packets.append(s_packet)
    ack_num += len(server_resp)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "pgsql_close_statement_portal.pcap")

def generate_flush_scenario():
    """生成Flush消息场景的数据包"""
    packets = []
    global seq_num, ack_num

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # 1. 客户端发送Parse消息
    parse_data = b"flush_stmt\0" + b"SELECT version();\0" + struct.pack("!H", 0)
    parse_msg = b"P" + struct.pack("!I", len(parse_data) + 4) + parse_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, parse_msg)
    packets.append(c_packet)
    seq_num += len(parse_msg)

    # 2. 客户端发送Flush消息强制输出缓冲
    flush_msg = b"H" + struct.pack("!I", 4)
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, flush_msg)
    packets.append(c_packet)
    seq_num += len(flush_msg)

    # 3. 服务器立即响应ParseComplete（由于Flush强制输出）
    parse_complete = b"1" + struct.pack("!I", 4)
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, parse_complete)
    packets.append(s_packet)
    ack_num += len(parse_complete)

    # 4. 客户端继续发送Bind消息
    bind_data = b"flush_portal\0flush_stmt\0" + struct.pack("!H", 0) + struct.pack("!H", 0) + struct.pack("!H", 0)
    bind_msg = b"B" + struct.pack("!I", len(bind_data) + 4) + bind_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, bind_msg)
    packets.append(c_packet)
    seq_num += len(bind_msg)

    # 5. 客户端发送Execute消息
    execute_data = b"flush_portal\0" + struct.pack("!I", 0)
    execute_msg = b"E" + struct.pack("!I", len(execute_data) + 4) + execute_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, execute_msg)
    packets.append(c_packet)
    seq_num += len(execute_msg)

    # 6. 客户端发送Sync消息
    sync_msg = b"S" + struct.pack("!I", 4)
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, sync_msg)
    packets.append(c_packet)
    seq_num += len(sync_msg)

    # 7. 服务器响应剩余消息
    bind_complete = b"2" + struct.pack("!I", 4)

    # 行描述
    row_desc_data = struct.pack("!H", 1) + b"version\0" + struct.pack("!IHIHIH", 0, 0, 25, 200, 200, 0)
    row_desc = b"T" + struct.pack("!I", len(row_desc_data) + 4) + row_desc_data

    # 数据行
    version_data = b"PostgreSQL 14.9 on x86_64-pc-linux-gnu"
    data_row_content = struct.pack("!H", 1) + struct.pack("!I", len(version_data)) + version_data
    data_row = b"D" + struct.pack("!I", len(data_row_content) + 4) + data_row_content

    cmd_complete_text = b"SELECT 1\0"
    cmd_complete = b"C" + struct.pack("!I", len(cmd_complete_text) + 4) + cmd_complete_text
    ready = b"Z" + struct.pack("!IB", 5, 73)

    server_resp = bind_complete + row_desc + data_row + cmd_complete + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp)
    packets.append(s_packet)
    ack_num += len(server_resp)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "pgsql_flush.pcap")

def generate_multi_parameter_scenario():
    """生成多参数预处理语句场景的数据包"""
    packets = []
    global seq_num, ack_num

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # 1. 客户端发送Parse消息（5个参数的复杂查询）
    query_sql = b"INSERT INTO orders (user_id, product_id, quantity, price, order_date) VALUES ($1, $2, $3, $4, $5);\0"
    parse_data = b"multi_param_stmt\0" + query_sql + struct.pack("!H", 5)  # 5个参数
    parse_data += struct.pack("!IIIII", 23, 23, 23, 701, 1082)  # int4, int4, int4, numeric, date
    parse_msg = b"P" + struct.pack("!I", len(parse_data) + 4) + parse_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, parse_msg)
    packets.append(c_packet)
    seq_num += len(parse_msg)

    # 2. 客户端发送Bind消息（绑定5个参数值）
    bind_data = b"multi_param_portal\0multi_param_stmt\0"
    bind_data += struct.pack("!H", 0)  # 所有参数使用默认格式（文本）
    bind_data += struct.pack("!H", 5)  # 5个参数值
    # 参数1: user_id = 123
    bind_data += struct.pack("!I", 3) + b"123"
    # 参数2: product_id = 456
    bind_data += struct.pack("!I", 3) + b"456"
    # 参数3: quantity = 2
    bind_data += struct.pack("!I", 1) + b"2"
    # 参数4: price = 99.99
    bind_data += struct.pack("!I", 5) + b"99.99"
    # 参数5: order_date = 2024-01-15
    bind_data += struct.pack("!I", 10) + b"2024-01-15"
    bind_data += struct.pack("!H", 0)  # 结果格式默认（文本）

    bind_msg = b"B" + struct.pack("!I", len(bind_data) + 4) + bind_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, bind_msg)
    packets.append(c_packet)
    seq_num += len(bind_msg)

    # 3. 客户端发送Execute消息
    execute_data = b"multi_param_portal\0" + struct.pack("!I", 0)
    execute_msg = b"E" + struct.pack("!I", len(execute_data) + 4) + execute_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, execute_msg)
    packets.append(c_packet)
    seq_num += len(execute_msg)

    # 4. 客户端发送Sync消息
    sync_msg = b"S" + struct.pack("!I", 4)
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, sync_msg)
    packets.append(c_packet)
    seq_num += len(sync_msg)

    # 5. 服务器响应
    parse_complete = b"1" + struct.pack("!I", 4)
    bind_complete = b"2" + struct.pack("!I", 4)
    cmd_complete = b"C" + struct.pack("!I", 15) + b"INSERT 0 1\0"
    ready = b"Z" + struct.pack("!IB", 5, 73)

    server_resp = parse_complete + bind_complete + cmd_complete + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp)
    packets.append(s_packet)
    ack_num += len(server_resp)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "pgsql_multi_parameter.pcap")

def generate_nodata_scenario():
    """生成NoData响应场景的数据包"""
    packets = []
    global seq_num, ack_num

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # 1. 客户端发送Parse消息（CREATE TABLE语句不返回行）
    parse_data = b"nodata_stmt\0" + b"CREATE TABLE temp_table (id int, name text);\0" + struct.pack("!H", 0)
    parse_msg = b"P" + struct.pack("!I", len(parse_data) + 4) + parse_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, parse_msg)
    packets.append(c_packet)
    seq_num += len(parse_msg)

    # 2. 客户端发送Describe消息描述预处理语句
    describe_data = b"S" + b"nodata_stmt\0"
    describe_msg = b"D" + struct.pack("!I", len(describe_data) + 4) + describe_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, describe_msg)
    packets.append(c_packet)
    seq_num += len(describe_msg)

    # 3. 客户端发送Sync消息
    sync_msg = b"S" + struct.pack("!I", 4)
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, sync_msg)
    packets.append(c_packet)
    seq_num += len(sync_msg)

    # 4. 服务器响应
    parse_complete = b"1" + struct.pack("!I", 4)

    # ParameterDescription（无参数）
    param_desc = b"t" + struct.pack("!I", 6) + struct.pack("!H", 0)

    # NoData响应（因为CREATE TABLE不返回行）
    nodata = b"n" + struct.pack("!I", 4)

    ready = b"Z" + struct.pack("!IB", 5, 73)

    server_resp = parse_complete + param_desc + nodata + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp)
    packets.append(s_packet)
    ack_num += len(server_resp)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "pgsql_nodata.pcap")

def generate_copy_to_stdout_scenario():
    """生成COPY TO STDOUT操作场景的数据包 - 从服务器导出数据到客户端"""
    packets = []
    global seq_num, ack_num

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # 1. 客户端发送COPY TO STDOUT命令
    copy_str = b"COPY users TO STDOUT WITH CSV HEADER;\0"
    copy_query = b"Q" + struct.pack("!I", len(copy_str) + 4) + copy_str
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, copy_query)
    packets.append(c_packet)
    seq_num += len(copy_query)

    # 2. 服务器响应CopyOutResponse，准备发送数据
    # 格式：0=文本，1=二进制；这里使用文本格式
    copy_out_data = struct.pack("!B", 0) + struct.pack("!H", 3)  # 文本格式，3列
    copy_out_data += struct.pack("!HHH", 0, 0, 0)  # 所有列都使用文本格式
    copy_out_response = b"H" + struct.pack("!I", len(copy_out_data) + 4) + copy_out_data
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, copy_out_response)
    packets.append(s_packet)
    ack_num += len(copy_out_response)

    # 3. 服务器发送CSV头部数据
    header_data = b"id,name,email\n"
    copy_data_header = b"d" + struct.pack("!I", len(header_data) + 4) + header_data
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, copy_data_header)
    packets.append(s_packet)
    ack_num += len(copy_data_header)

    # 4. 服务器发送多行数据
    data_rows = [
        b"1,Alice,<EMAIL>\n",
        b"2,Bob,<EMAIL>\n",
        b"3,Charlie,<EMAIL>\n",
        b"4,Diana,<EMAIL>\n",
        b"5,Eve,<EMAIL>\n"
    ]

    for row_data in data_rows:
        copy_data_row = b"d" + struct.pack("!I", len(row_data) + 4) + row_data
        s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, copy_data_row)
        packets.append(s_packet)
        ack_num += len(copy_data_row)

    # 5. 服务器发送CopyDone表示数据传输完成
    copy_done = b"c" + struct.pack("!I", 4)
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, copy_done)
    packets.append(s_packet)
    ack_num += len(copy_done)

    # 6. 服务器发送CommandComplete
    cmd_complete_data = b"COPY 5\0"
    cmd_complete = b"C" + struct.pack("!I", len(cmd_complete_data) + 4) + cmd_complete_data
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, cmd_complete)
    packets.append(s_packet)
    ack_num += len(cmd_complete)

    # 7. ReadyForQuery
    ready = b"Z" + struct.pack("!I", 5) + b"I"
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, ready)
    packets.append(s_packet)
    ack_num += len(ready)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "pgsql_copy_to_stdout.pcap")

def generate_copy_fail_scenario():
    """生成COPY操作错误处理场景的数据包 - CopyFail消息"""
    packets = []
    global seq_num, ack_num

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # 1. 客户端发送COPY FROM STDIN命令
    copy_str = b"COPY users FROM STDIN WITH CSV HEADER;\0"
    copy_query = b"Q" + struct.pack("!I", len(copy_str) + 4) + copy_str
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, copy_query)
    packets.append(c_packet)
    seq_num += len(copy_query)

    # 2. 服务器响应CopyInResponse，准备接收数据
    copy_in_data = struct.pack("!B", 0) + struct.pack("!H", 3)  # 文本格式，3列
    copy_in_data += struct.pack("!HHH", 0, 0, 0)  # 所有列都使用文本格式
    copy_in_response = b"G" + struct.pack("!I", len(copy_in_data) + 4) + copy_in_data
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, copy_in_response)
    packets.append(s_packet)
    ack_num += len(copy_in_response)

    # 3. 客户端发送一些正常数据
    header_data = b"id,name,email\n"
    copy_data_header = b"d" + struct.pack("!I", len(header_data) + 4) + header_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, copy_data_header)
    packets.append(c_packet)
    seq_num += len(copy_data_header)

    # 4. 客户端发送一行正常数据
    normal_data = b"1,Alice,<EMAIL>\n"
    copy_data_normal = b"d" + struct.pack("!I", len(normal_data) + 4) + normal_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, copy_data_normal)
    packets.append(c_packet)
    seq_num += len(copy_data_normal)

    # 5. 客户端检测到错误并发送CopyFail消息
    error_message = b"Invalid data format in line 3\0"
    copy_fail = b"f" + struct.pack("!I", len(error_message) + 4) + error_message
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, copy_fail)
    packets.append(c_packet)
    seq_num += len(copy_fail)

    # 6. 服务器响应ErrorResponse
    error_resp = b"E" + struct.pack("!I", 95)
    error_resp += b"SERROR\0"  # 严重性
    error_resp += b"C22P04\0"  # 错误代码：bad_copy_file_format
    error_resp += b"MCOPY operation failed: Invalid data format in line 3\0"  # 错误消息
    error_resp += b"Fcopy.c\0"  # 文件名
    error_resp += b"L789\0"  # 行号
    error_resp += b"RCopyFrom\0"  # 例程名
    error_resp += b"\0"  # 终止符

    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, error_resp)
    packets.append(s_packet)
    ack_num += len(error_resp)

    # 7. ReadyForQuery
    ready = b"Z" + struct.pack("!I", 5) + b"I"
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, ready)
    packets.append(s_packet)
    ack_num += len(ready)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "pgsql_copy_fail.pcap", is_abnormal=True)

def generate_binary_copy_scenario():
    """生成二进制格式COPY操作场景的数据包"""
    packets = []
    global seq_num, ack_num

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # 1. 客户端发送二进制格式COPY FROM STDIN命令
    copy_str = b"COPY binary_data FROM STDIN WITH BINARY;\0"
    copy_query = b"Q" + struct.pack("!I", len(copy_str) + 4) + copy_str
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, copy_query)
    packets.append(c_packet)
    seq_num += len(copy_query)

    # 2. 服务器响应CopyInResponse，二进制格式
    copy_in_data = struct.pack("!B", 1) + struct.pack("!H", 3)  # 二进制格式，3列
    copy_in_data += struct.pack("!HHH", 1, 1, 1)  # 所有列都使用二进制格式
    copy_in_response = b"G" + struct.pack("!I", len(copy_in_data) + 4) + copy_in_data
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, copy_in_response)
    packets.append(s_packet)
    ack_num += len(copy_in_response)

    # 3. 客户端发送二进制COPY数据头部
    # PostgreSQL二进制COPY格式头部：签名 + 标志字段 + 头部扩展长度
    binary_header = b"PGCOPY\n\xff\r\n\0"  # 11字节签名
    binary_header += struct.pack("!I", 0)  # 标志字段（0表示无特殊标志）
    binary_header += struct.pack("!I", 0)  # 头部扩展长度（0表示无扩展）

    copy_data_header = b"d" + struct.pack("!I", len(binary_header) + 4) + binary_header
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, copy_data_header)
    packets.append(c_packet)
    seq_num += len(copy_data_header)

    # 4. 客户端发送二进制数据行
    # 第一行数据：id=1, name="Alice", data=binary_blob
    row1_data = struct.pack("!H", 3)  # 3个字段
    # 字段1：id (int4) = 1
    row1_data += struct.pack("!I", 4) + struct.pack("!i", 1)
    # 字段2：name (text) = "Alice"
    name_bytes = b"Alice"
    row1_data += struct.pack("!I", len(name_bytes)) + name_bytes
    # 字段3：data (bytea) = 二进制数据
    binary_blob = bytes([0x01, 0x02, 0x03, 0x04, 0x05, 0xAA, 0xBB, 0xCC])
    row1_data += struct.pack("!I", len(binary_blob)) + binary_blob

    copy_data_row1 = b"d" + struct.pack("!I", len(row1_data) + 4) + row1_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, copy_data_row1)
    packets.append(c_packet)
    seq_num += len(copy_data_row1)

    # 5. 客户端发送第二行二进制数据
    row2_data = struct.pack("!H", 3)  # 3个字段
    # 字段1：id (int4) = 2
    row2_data += struct.pack("!I", 4) + struct.pack("!i", 2)
    # 字段2：name (text) = "Bob"
    name_bytes = b"Bob"
    row2_data += struct.pack("!I", len(name_bytes)) + name_bytes
    # 字段3：data (bytea) = 不同的二进制数据
    binary_blob = bytes([0xFF, 0xEE, 0xDD, 0xCC, 0xBB, 0xAA])
    row2_data += struct.pack("!I", len(binary_blob)) + binary_blob

    copy_data_row2 = b"d" + struct.pack("!I", len(row2_data) + 4) + row2_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, copy_data_row2)
    packets.append(c_packet)
    seq_num += len(copy_data_row2)

    # 6. 客户端发送二进制COPY结束标记
    # PostgreSQL二进制COPY格式结束标记：-1 (0xFFFF)
    binary_trailer = struct.pack("!h", -1)
    copy_data_trailer = b"d" + struct.pack("!I", len(binary_trailer) + 4) + binary_trailer
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, copy_data_trailer)
    packets.append(c_packet)
    seq_num += len(copy_data_trailer)

    # 7. 客户端发送CopyDone
    copy_done = b"c" + struct.pack("!I", 4)
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, copy_done)
    packets.append(c_packet)
    seq_num += len(copy_done)

    # 8. 服务器响应CommandComplete
    cmd_complete_data = b"COPY 2\0"
    cmd_complete = b"C" + struct.pack("!I", len(cmd_complete_data) + 4) + cmd_complete_data
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, cmd_complete)
    packets.append(s_packet)
    ack_num += len(cmd_complete)

    # 9. ReadyForQuery
    ready = b"Z" + struct.pack("!I", 5) + b"I"
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, ready)
    packets.append(s_packet)
    ack_num += len(ready)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "pgsql_binary_copy.pcap")

def generate_syntax_error_scenario():
    """生成SQL语法错误场景的数据包"""
    packets = []
    global seq_num, ack_num

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # 1. 客户端发送语法错误的查询
    # 故意的语法错误：缺少FROM关键字
    query_str = b"SELECT name, email users WHERE id = 1;\0"
    syntax_error_query = b"Q" + struct.pack("!I", len(query_str) + 4) + query_str
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, syntax_error_query)
    packets.append(c_packet)
    seq_num += len(syntax_error_query)

    # 2. 服务器返回语法错误响应
    error_content = b"SERROR\0"  # 严重性：ERROR
    error_content += b"C42601\0"  # 错误代码：syntax_error
    error_content += b"Msyntax error at or near \"users\"\0"  # 错误消息
    error_content += b"P21\0"  # 错误位置（字符位置）
    error_content += b"Fparse_relation.c\0"  # 文件名
    error_content += b"L1234\0"  # 行号
    error_content += b"Rparser\0"  # 例程名
    error_content += b"\0"  # 终止符
    error_resp = b"E" + struct.pack("!I", len(error_content) + 4) + error_content

    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, error_resp)
    packets.append(s_packet)
    ack_num += len(error_resp)

    # 3. ReadyForQuery
    ready = b"Z" + struct.pack("!I", 5) + b"I"
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, ready)
    packets.append(s_packet)
    ack_num += len(ready)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "pgsql_syntax_error.pcap", is_abnormal=True)

def generate_type_error_scenario():
    """生成数据类型错误场景的数据包"""
    packets = []
    global seq_num, ack_num

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # 1. 客户端发送类型不匹配的查询
    # 尝试将字符串与整数进行数学运算
    query_str = b"SELECT 'hello' + 123 AS invalid_operation;\0"
    type_error_query = b"Q" + struct.pack("!I", len(query_str) + 4) + query_str
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, type_error_query)
    packets.append(c_packet)
    seq_num += len(type_error_query)

    # 2. 服务器返回类型错误响应
    error_content = b"SERROR\0"  # 严重性：ERROR
    error_content += b"C42883\0"  # 错误代码：undefined_function
    error_content += b"Moperator does not exist: unknown + integer\0"  # 错误消息
    error_content += b"HNo operator matches the given name and argument types. You might need to add explicit type casts.\0"  # 提示
    error_content += b"P8\0"  # 错误位置
    error_content += b"Fparse_oper.c\0"  # 文件名
    error_content += b"L567\0"  # 行号
    error_content += b"Roper_select\0"  # 例程名
    error_content += b"\0"  # 终止符
    error_resp = b"E" + struct.pack("!I", len(error_content) + 4) + error_content

    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, error_resp)
    packets.append(s_packet)
    ack_num += len(error_resp)

    # 3. ReadyForQuery
    ready = b"Z" + struct.pack("!I", 5) + b"I"
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, ready)
    packets.append(s_packet)
    ack_num += len(ready)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "pgsql_type_error.pcap", is_abnormal=True)

def generate_constraint_violation_scenario():
    """生成约束冲突错误场景的数据包"""
    packets = []
    global seq_num, ack_num

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # 1. 客户端发送违反主键约束的INSERT语句
    query_str = b"INSERT INTO users (id, name, email) VALUES (1, 'Duplicate', '<EMAIL>');\0"
    constraint_error_query = b"Q" + struct.pack("!I", len(query_str) + 4) + query_str
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, constraint_error_query)
    packets.append(c_packet)
    seq_num += len(constraint_error_query)

    # 2. 服务器返回约束违反错误响应
    error_content = b"SERROR\0"  # 严重性：ERROR
    error_content += b"C23505\0"  # 错误代码：unique_violation
    error_content += b"Mduplicate key value violates unique constraint \"users_pkey\"\0"  # 错误消息
    error_content += b"DKey (id)=(1) already exists.\0"  # 详细信息
    error_content += b"suser_table\0"  # 模式名
    error_content += b"tusers\0"  # 表名
    error_content += b"nusers_pkey\0"  # 约束名
    error_content += b"Fexecutils.c\0"  # 文件名
    error_content += b"L890\0"  # 行号
    error_content += b"RExecConstraints\0"  # 例程名
    error_content += b"\0"  # 终止符
    error_resp = b"E" + struct.pack("!I", len(error_content) + 4) + error_content

    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, error_resp)
    packets.append(s_packet)
    ack_num += len(error_resp)

    # 3. ReadyForQuery
    ready = b"Z" + struct.pack("!I", 5) + b"I"
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, ready)
    packets.append(s_packet)
    ack_num += len(ready)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "pgsql_constraint_violation.pcap", is_abnormal=True)

def generate_fatal_error_scenario():
    """生成FATAL级别错误场景的数据包"""
    packets = []
    global seq_num, ack_num

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # 1. 客户端发送会导致FATAL错误的查询（如访问不存在的数据库）
    fatal_error_query = b"Q" + struct.pack("!I", 35) + b"SELECT * FROM nonexistent_database.table;\0"
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, fatal_error_query)
    packets.append(c_packet)
    seq_num += len(fatal_error_query)

    # 2. 服务器返回FATAL级别错误响应
    error_resp = b"E" + struct.pack("!I", 135)
    error_resp += b"SFATAL\0"  # 严重性：FATAL
    error_resp += b"C3D000\0"  # 错误代码：invalid_catalog_name
    error_resp += b"Mdatabase \"nonexistent_database\" does not exist\0"  # 错误消息
    error_resp += b"Fpostinit.c\0"  # 文件名
    error_resp += b"L456\0"  # 行号
    error_resp += b"RInitPostgres\0"  # 例程名
    error_resp += b"\0"  # 终止符

    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, error_resp)
    packets.append(s_packet)
    ack_num += len(error_resp)

    # 3. FATAL错误后服务器会关闭连接，不发送ReadyForQuery
    # 直接进行TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "pgsql_fatal_error.pcap", is_abnormal=True)

def generate_panic_error_scenario():
    """生成PANIC级别错误场景的数据包"""
    packets = []
    global seq_num, ack_num

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # 1. 客户端发送可能导致PANIC的查询（模拟系统级严重错误）
    panic_query = b"Q" + struct.pack("!I", 25) + b"SELECT pg_crash_server();\0"
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, panic_query)
    packets.append(c_packet)
    seq_num += len(panic_query)

    # 2. 服务器返回PANIC级别错误响应
    error_resp = b"E" + struct.pack("!I", 125)
    error_resp += b"SPANIC\0"  # 严重性：PANIC
    error_resp += b"CXX000\0"  # 错误代码：internal_error
    error_resp += b"MPANIC: system is shutting down\0"  # 错误消息
    error_resp += b"Fpostmaster.c\0"  # 文件名
    error_resp += b"L2345\0"  # 行号
    error_resp += b"RServerLoop\0"  # 例程名
    error_resp += b"\0"  # 终止符

    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, error_resp)
    packets.append(s_packet)
    ack_num += len(error_resp)

    # 3. PANIC错误后服务器会立即关闭连接
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "pgsql_panic_error.pcap", is_abnormal=True)

def generate_notice_response_scenario():
    """生成NoticeResponse消息场景的数据包"""
    packets = []
    global seq_num, ack_num

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # 1. 客户端发送会产生警告的查询
    query_str = b"CREATE TABLE IF NOT EXISTS existing_table (id int);\0"
    notice_query = b"Q" + struct.pack("!I", len(query_str) + 4) + query_str
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, notice_query)
    packets.append(c_packet)
    seq_num += len(notice_query)

    # 2. 服务器发送NoticeResponse（表已存在的警告）
    notice_content = b"SNOTICE\0"  # 严重性：NOTICE
    notice_content += b"C42P07\0"  # 代码：duplicate_table
    notice_content += b"Mrelation \"existing_table\" already exists, skipping\0"  # 消息
    notice_content += b"Ftablecmds.c\0"  # 文件名
    notice_content += b"L1123\0"  # 行号
    notice_content += b"RDefineRelation\0"  # 例程名
    notice_content += b"\0"  # 终止符
    notice_resp = b"N" + struct.pack("!I", len(notice_content) + 4) + notice_content

    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, notice_resp)
    packets.append(s_packet)
    ack_num += len(notice_resp)

    # 3. 服务器继续发送CommandComplete（命令成功完成）
    cmd_complete_text = b"CREATE TABLE\0"
    cmd_complete = b"C" + struct.pack("!I", len(cmd_complete_text) + 4) + cmd_complete_text
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, cmd_complete)
    packets.append(s_packet)
    ack_num += len(cmd_complete)

    # 4. ReadyForQuery
    ready_content = b"I"  # 空闲状态
    ready = b"Z" + struct.pack("!I", len(ready_content) + 4) + ready_content
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, ready)
    packets.append(s_packet)
    ack_num += len(ready)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "pgsql_notice_response.pcap")

def generate_multiple_data_types_scenario():
    """生成多种数据类型测试场景的数据包"""
    packets = []
    global seq_num, ack_num

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # 1. 客户端发送包含多种数据类型的查询
    query_sql = b"SELECT 42::int4, 3.14159::float4, 2.718281828::float8, 'Hello World'::text, " \
                b"'2024-01-15'::date, '2024-01-15 14:30:00'::timestamp, " \
                b"'{\"name\": \"test\", \"value\": 123}'::json, " \
                b"'550e8400-e29b-41d4-a716-************'::uuid, " \
                b"'\\x48656c6c6f'::bytea, " \
                b"ARRAY[1,2,3,4,5]::int[], " \
                b"NULL::text AS null_value;\0"

    query = b"Q" + struct.pack("!I", len(query_sql) + 4) + query_sql
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, query)
    packets.append(c_packet)
    seq_num += len(query)

    # 2. 服务器响应行描述（11列，不同数据类型）
    row_desc_data = struct.pack("!H", 11)  # 11列

    # int4列
    row_desc_data += b"int4\0" + struct.pack("!IHIhih", 0, 0, 23, 4, 4, 0)
    # float4列
    row_desc_data += b"float4\0" + struct.pack("!IHIhih", 0, 0, 700, 4, 4, 0)
    # float8列
    row_desc_data += b"float8\0" + struct.pack("!IHIhih", 0, 0, 701, 8, 8, 0)
    # text列
    row_desc_data += b"text\0" + struct.pack("!IHIhih", 0, 0, 25, -1, -1, 0)
    # date列
    row_desc_data += b"date\0" + struct.pack("!IHIhih", 0, 0, 1082, 4, 4, 0)
    # timestamp列
    row_desc_data += b"timestamp\0" + struct.pack("!IHIhih", 0, 0, 1114, 8, 8, 0)
    # json列
    row_desc_data += b"json\0" + struct.pack("!IHIhih", 0, 0, 114, -1, -1, 0)
    # uuid列
    row_desc_data += b"uuid\0" + struct.pack("!IHIhih", 0, 0, 2950, 16, 16, 0)
    # bytea列
    row_desc_data += b"bytea\0" + struct.pack("!IHIhih", 0, 0, 17, -1, -1, 0)
    # int[]列
    row_desc_data += b"int4_array\0" + struct.pack("!IHIhih", 0, 0, 1007, -1, -1, 0)
    # null_value列
    row_desc_data += b"null_value\0" + struct.pack("!IHIhih", 0, 0, 25, -1, -1, 0)

    row_desc = b"T" + struct.pack("!I", len(row_desc_data) + 4) + row_desc_data

    # 3. 服务器发送数据行
    data_row_content = struct.pack("!H", 11)  # 11列

    # int4值: 42
    int4_val = b"42"
    data_row_content += struct.pack("!I", len(int4_val)) + int4_val

    # float4值: 3.14159
    float4_val = b"3.14159"
    data_row_content += struct.pack("!I", len(float4_val)) + float4_val

    # float8值: 2.718281828
    float8_val = b"2.718281828"
    data_row_content += struct.pack("!I", len(float8_val)) + float8_val

    # text值: Hello World
    text_val = b"Hello World"
    data_row_content += struct.pack("!I", len(text_val)) + text_val

    # date值: 2024-01-15
    date_val = b"2024-01-15"
    data_row_content += struct.pack("!I", len(date_val)) + date_val

    # timestamp值: 2024-01-15 14:30:00
    timestamp_val = b"2024-01-15 14:30:00"
    data_row_content += struct.pack("!I", len(timestamp_val)) + timestamp_val

    # json值: {"name": "test", "value": 123}
    json_val = b'{"name": "test", "value": 123}'
    data_row_content += struct.pack("!I", len(json_val)) + json_val

    # uuid值: 550e8400-e29b-41d4-a716-************
    uuid_val = b"550e8400-e29b-41d4-a716-************"
    data_row_content += struct.pack("!I", len(uuid_val)) + uuid_val

    # bytea值: \x48656c6c6f (Hello的十六进制)
    bytea_val = b"\\x48656c6c6f"
    data_row_content += struct.pack("!I", len(bytea_val)) + bytea_val

    # int[]值: {1,2,3,4,5}
    array_val = b"{1,2,3,4,5}"
    data_row_content += struct.pack("!I", len(array_val)) + array_val

    # NULL值
    data_row_content += struct.pack("!i", -1)  # -1表示NULL

    data_row = b"D" + struct.pack("!I", len(data_row_content) + 4) + data_row_content

    # 4. 命令完成
    cmd_complete_text = b"SELECT 1\0"
    cmd_complete = b"C" + struct.pack("!I", len(cmd_complete_text) + 4) + cmd_complete_text

    # 5. 准备就绪
    ready = b"Z" + struct.pack("!IB", 5, 73)

    server_resp = row_desc + data_row + cmd_complete + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp)
    packets.append(s_packet)
    ack_num += len(server_resp)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "pgsql_multiple_data_types.pcap")

def generate_character_encoding_scenario():
    """生成字符编码测试场景的数据包 - 模拟client_encoding参数变化"""
    packets = []
    global seq_num, ack_num

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # 1. 客户端发送SET client_encoding命令
    sql_text = b"SET client_encoding = 'UTF8';\0"
    set_encoding_query = b"Q" + struct.pack("!I", len(sql_text) + 4) + sql_text
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, set_encoding_query)
    packets.append(c_packet)
    seq_num += len(set_encoding_query)

    # 2. 服务器响应ParameterStatus消息（编码变化通知）
    param_status_data = b"client_encoding\0UTF8\0"
    param_status = b"S" + struct.pack("!I", len(param_status_data) + 4) + param_status_data

    # CommandComplete
    cmd_complete_text = b"SET\0"
    cmd_complete = b"C" + struct.pack("!I", len(cmd_complete_text) + 4) + cmd_complete_text

    # ReadyForQuery
    ready = b"Z" + struct.pack("!IB", 5, 73)

    server_resp = param_status + cmd_complete + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp)
    packets.append(s_packet)
    ack_num += len(server_resp)

    # 3. 客户端发送包含UTF-8字符的查询
    utf8_sql = b"SELECT '\xe4\xb8\xad\xe6\x96\x87' AS chinese, '\xc3\xa9\xc3\xa0\xc3\xa8' AS french, '\xd0\x9f\xd1\x80\xd0\xb8\xd0\xb2\xd0\xb5\xd1\x82' AS russian;\0"
    utf8_query = b"Q" + struct.pack("!I", len(utf8_sql) + 4) + utf8_sql
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, utf8_query)
    packets.append(c_packet)
    seq_num += len(utf8_query)

    # 4. 服务器响应UTF-8数据
    # 行描述
    row_desc_data = struct.pack("!H", 3)  # 3列
    row_desc_data += b"chinese\0" + struct.pack("!IHIhih", 0, 0, 25, -1, -1, 0)
    row_desc_data += b"french\0" + struct.pack("!IHIhih", 0, 0, 25, -1, -1, 0)
    row_desc_data += b"russian\0" + struct.pack("!IHIhih", 0, 0, 25, -1, -1, 0)
    row_desc = b"T" + struct.pack("!I", len(row_desc_data) + 4) + row_desc_data

    # 数据行（UTF-8编码的文本）
    data_row_content = struct.pack("!H", 3)  # 3列
    # 中文
    chinese_text = b"\xe4\xb8\xad\xe6\x96\x87"  # "中文"的UTF-8编码
    data_row_content += struct.pack("!I", len(chinese_text)) + chinese_text
    # 法文
    french_text = b"\xc3\xa9\xc3\xa0\xc3\xa8"  # "éàè"的UTF-8编码
    data_row_content += struct.pack("!I", len(french_text)) + french_text
    # 俄文
    russian_text = b"\xd0\x9f\xd1\x80\xd0\xb8\xd0\xb2\xd0\xb5\xd1\x82"  # "Привет"的UTF-8编码
    data_row_content += struct.pack("!I", len(russian_text)) + russian_text

    data_row = b"D" + struct.pack("!I", len(data_row_content) + 4) + data_row_content

    cmd_complete_text = b"SELECT 1\0"
    cmd_complete = b"C" + struct.pack("!I", len(cmd_complete_text) + 4) + cmd_complete_text
    ready = b"Z" + struct.pack("!IB", 5, 73)

    server_resp = row_desc + data_row + cmd_complete + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp)
    packets.append(s_packet)
    ack_num += len(server_resp)

    # 5. 客户端更改编码为Latin-1
    latin1_sql = b"SET client_encoding = 'LATIN1';\0"
    set_latin1_query = b"Q" + struct.pack("!I", len(latin1_sql) + 4) + latin1_sql
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, set_latin1_query)
    packets.append(c_packet)
    seq_num += len(set_latin1_query)

    # 6. 服务器响应编码变化
    param_status_latin1_data = b"client_encoding\0LATIN1\0"
    param_status_latin1 = b"S" + struct.pack("!I", len(param_status_latin1_data) + 4) + param_status_latin1_data
    cmd_complete_text = b"SET\0"
    cmd_complete = b"C" + struct.pack("!I", len(cmd_complete_text) + 4) + cmd_complete_text
    ready = b"Z" + struct.pack("!IB", 5, 73)

    server_resp = param_status_latin1 + cmd_complete + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp)
    packets.append(s_packet)
    ack_num += len(server_resp)

    # 7. 客户端发送Latin-1字符查询
    latin1_query_sql = b"SELECT '\xe9\xe0\xe8' AS latin1_chars;\0"  # éàè的Latin-1编码
    latin1_query = b"Q" + struct.pack("!I", len(latin1_query_sql) + 4) + latin1_query_sql
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, latin1_query)
    packets.append(c_packet)
    seq_num += len(latin1_query)

    # 8. 服务器响应Latin-1数据
    row_desc_data = struct.pack("!H", 1) + b"latin1_chars\0" + struct.pack("!IHIhih", 0, 0, 25, -1, -1, 0)
    row_desc = b"T" + struct.pack("!I", len(row_desc_data) + 4) + row_desc_data

    data_row_content = struct.pack("!H", 1)
    latin1_text = b"\xe9\xe0\xe8"  # éàè的Latin-1编码
    data_row_content += struct.pack("!I", len(latin1_text)) + latin1_text
    data_row = b"D" + struct.pack("!I", len(data_row_content) + 4) + data_row_content

    cmd_complete_text = b"SELECT 1\0"
    cmd_complete = b"C" + struct.pack("!I", len(cmd_complete_text) + 4) + cmd_complete_text
    ready = b"Z" + struct.pack("!IB", 5, 73)

    server_resp = row_desc + data_row + cmd_complete + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp)
    packets.append(s_packet)
    ack_num += len(server_resp)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "pgsql_character_encoding.pcap")

def generate_pipelined_queries_scenario():
    """生成管道化查询测试场景的数据包 - 发送多个查询而不等待响应"""
    packets = []
    global seq_num, ack_num

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # 1. 客户端连续发送多个Parse消息（不等待响应）
    # Parse消息1
    parse1_data = b"stmt1\0SELECT 1 AS first_query;\0" + struct.pack("!H", 0)
    parse1_msg = b"P" + struct.pack("!I", len(parse1_data) + 4) + parse1_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, parse1_msg)
    packets.append(c_packet)
    seq_num += len(parse1_msg)

    # Parse消息2
    parse2_data = b"stmt2\0SELECT 2 AS second_query;\0" + struct.pack("!H", 0)
    parse2_msg = b"P" + struct.pack("!I", len(parse2_data) + 4) + parse2_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, parse2_msg)
    packets.append(c_packet)
    seq_num += len(parse2_msg)

    # Parse消息3
    parse3_data = b"stmt3\0SELECT 3 AS third_query;\0" + struct.pack("!H", 0)
    parse3_msg = b"P" + struct.pack("!I", len(parse3_data) + 4) + parse3_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, parse3_msg)
    packets.append(c_packet)
    seq_num += len(parse3_msg)

    # 2. 客户端连续发送对应的Bind消息
    # Bind消息1
    bind1_data = b"portal1\0stmt1\0" + struct.pack("!HHH", 0, 0, 0)
    bind1_msg = b"B" + struct.pack("!I", len(bind1_data) + 4) + bind1_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, bind1_msg)
    packets.append(c_packet)
    seq_num += len(bind1_msg)

    # Bind消息2
    bind2_data = b"portal2\0stmt2\0" + struct.pack("!HHH", 0, 0, 0)
    bind2_msg = b"B" + struct.pack("!I", len(bind2_data) + 4) + bind2_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, bind2_msg)
    packets.append(c_packet)
    seq_num += len(bind2_msg)

    # Bind消息3
    bind3_data = b"portal3\0stmt3\0" + struct.pack("!HHH", 0, 0, 0)
    bind3_msg = b"B" + struct.pack("!I", len(bind3_data) + 4) + bind3_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, bind3_msg)
    packets.append(c_packet)
    seq_num += len(bind3_msg)

    # 3. 客户端连续发送Execute消息
    # Execute消息1
    exec1_data = b"portal1\0" + struct.pack("!I", 0)
    exec1_msg = b"E" + struct.pack("!I", len(exec1_data) + 4) + exec1_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, exec1_msg)
    packets.append(c_packet)
    seq_num += len(exec1_msg)

    # Execute消息2
    exec2_data = b"portal2\0" + struct.pack("!I", 0)
    exec2_msg = b"E" + struct.pack("!I", len(exec2_data) + 4) + exec2_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, exec2_msg)
    packets.append(c_packet)
    seq_num += len(exec2_msg)

    # Execute消息3
    exec3_data = b"portal3\0" + struct.pack("!I", 0)
    exec3_msg = b"E" + struct.pack("!I", len(exec3_data) + 4) + exec3_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, exec3_msg)
    packets.append(c_packet)
    seq_num += len(exec3_msg)

    # 4. 客户端发送Sync消息
    sync_msg = b"S" + struct.pack("!I", 4)
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, sync_msg)
    packets.append(c_packet)
    seq_num += len(sync_msg)

    # 5. 服务器批量响应所有消息
    server_responses = b""

    # ParseComplete消息 x3
    server_responses += b"1" + struct.pack("!I", 4)
    server_responses += b"1" + struct.pack("!I", 4)
    server_responses += b"1" + struct.pack("!I", 4)

    # BindComplete消息 x3
    server_responses += b"2" + struct.pack("!I", 4)
    server_responses += b"2" + struct.pack("!I", 4)
    server_responses += b"2" + struct.pack("!I", 4)

    # 第一个查询的结果
    row_desc1_data = struct.pack("!H", 1) + b"first_query\0" + struct.pack("!IHIHIH", 0, 0, 23, 4, 4, 0)
    row_desc1 = b"T" + struct.pack("!I", len(row_desc1_data) + 4) + row_desc1_data
    data_row1_content = struct.pack("!H", 1) + struct.pack("!I", 1) + b"1"
    data_row1 = b"D" + struct.pack("!I", len(data_row1_content) + 4) + data_row1_content
    cmd_complete_text1 = b"SELECT 1\0"
    cmd_complete1 = b"C" + struct.pack("!I", len(cmd_complete_text1) + 4) + cmd_complete_text1

    # 第二个查询的结果
    row_desc2_data = struct.pack("!H", 1) + b"second_query\0" + struct.pack("!IHIhih", 0, 0, 23, 4, 4, 0)
    row_desc2 = b"T" + struct.pack("!I", len(row_desc2_data) + 4) + row_desc2_data
    data_row2_content = struct.pack("!H", 1) + struct.pack("!I", 1) + b"2"
    data_row2 = b"D" + struct.pack("!I", len(data_row2_content) + 4) + data_row2_content
    cmd_complete_text2 = b"SELECT 1\0"
    cmd_complete2 = b"C" + struct.pack("!I", len(cmd_complete_text2) + 4) + cmd_complete_text2

    # 第三个查询的结果
    row_desc3_data = struct.pack("!H", 1) + b"third_query\0" + struct.pack("!IHIhih", 0, 0, 23, 4, 4, 0)
    row_desc3 = b"T" + struct.pack("!I", len(row_desc3_data) + 4) + row_desc3_data
    data_row3_content = struct.pack("!H", 1) + struct.pack("!I", 1) + b"3"
    data_row3 = b"D" + struct.pack("!I", len(data_row3_content) + 4) + data_row3_content
    cmd_complete_text3 = b"SELECT 1\0"
    cmd_complete3 = b"C" + struct.pack("!I", len(cmd_complete_text3) + 4) + cmd_complete_text3

    # ReadyForQuery
    ready = b"Z" + struct.pack("!IB", 5, 73)

    server_responses += row_desc1 + data_row1 + cmd_complete1
    server_responses += row_desc2 + data_row2 + cmd_complete2
    server_responses += row_desc3 + data_row3 + cmd_complete3
    server_responses += ready

    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_responses)
    packets.append(s_packet)
    ack_num += len(server_responses)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "pgsql_pipelined_queries.pcap")

def generate_sync_separated_transactions_scenario():
    """生成Sync消息分隔事务段场景的数据包"""
    packets = []
    global seq_num, ack_num

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # 第一个事务段：BEGIN + INSERT
    # 1. Parse BEGIN
    parse_begin_data = b"begin_stmt\0BEGIN;\0" + struct.pack("!H", 0)
    parse_begin_msg = b"P" + struct.pack("!I", len(parse_begin_data) + 4) + parse_begin_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, parse_begin_msg)
    packets.append(c_packet)
    seq_num += len(parse_begin_msg)

    # 2. Bind BEGIN
    bind_begin_data = b"begin_portal\0begin_stmt\0" + struct.pack("!HHH", 0, 0, 0)
    bind_begin_msg = b"B" + struct.pack("!I", len(bind_begin_data) + 4) + bind_begin_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, bind_begin_msg)
    packets.append(c_packet)
    seq_num += len(bind_begin_msg)

    # 3. Execute BEGIN
    exec_begin_data = b"begin_portal\0" + struct.pack("!I", 0)
    exec_begin_msg = b"E" + struct.pack("!I", len(exec_begin_data) + 4) + exec_begin_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, exec_begin_msg)
    packets.append(c_packet)
    seq_num += len(exec_begin_msg)

    # 4. Parse INSERT
    parse_insert_data = b"insert_stmt\0INSERT INTO test_table (id, name) VALUES (1, 'test');\0" + struct.pack("!H", 0)
    parse_insert_msg = b"P" + struct.pack("!I", len(parse_insert_data) + 4) + parse_insert_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, parse_insert_msg)
    packets.append(c_packet)
    seq_num += len(parse_insert_msg)

    # 5. Bind INSERT
    bind_insert_data = b"insert_portal\0insert_stmt\0" + struct.pack("!HHH", 0, 0, 0)
    bind_insert_msg = b"B" + struct.pack("!I", len(bind_insert_data) + 4) + bind_insert_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, bind_insert_msg)
    packets.append(c_packet)
    seq_num += len(bind_insert_msg)

    # 6. Execute INSERT
    exec_insert_data = b"insert_portal\0" + struct.pack("!I", 0)
    exec_insert_msg = b"E" + struct.pack("!I", len(exec_insert_data) + 4) + exec_insert_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, exec_insert_msg)
    packets.append(c_packet)
    seq_num += len(exec_insert_msg)

    # 7. 第一个Sync消息（结束第一个事务段）
    sync1_msg = b"S" + struct.pack("!I", 4)
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, sync1_msg)
    packets.append(c_packet)
    seq_num += len(sync1_msg)

    # 8. 服务器响应第一个事务段
    server_resp1 = b""
    server_resp1 += b"1" + struct.pack("!I", 4)  # ParseComplete for BEGIN
    server_resp1 += b"2" + struct.pack("!I", 4)  # BindComplete for BEGIN
    # CommandComplete for BEGIN - 动态计算长度
    begin_cmd_text = b"BEGIN\0"
    server_resp1 += b"C" + struct.pack("!I", len(begin_cmd_text) + 4) + begin_cmd_text
    server_resp1 += b"1" + struct.pack("!I", 4)  # ParseComplete for INSERT
    server_resp1 += b"2" + struct.pack("!I", 4)  # BindComplete for INSERT
    # CommandComplete for INSERT - 动态计算长度
    insert_cmd_text = b"INSERT 0 1\0"
    server_resp1 += b"C" + struct.pack("!I", len(insert_cmd_text) + 4) + insert_cmd_text
    server_resp1 += b"Z" + struct.pack("!IB", 5, 84)  # ReadyForQuery (T=在事务中)

    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp1)
    packets.append(s_packet)
    ack_num += len(server_resp1)

    # 第二个事务段：SELECT + COMMIT
    # 9. Parse SELECT
    parse_select_data = b"select_stmt\0SELECT * FROM test_table;\0" + struct.pack("!H", 0)
    parse_select_msg = b"P" + struct.pack("!I", len(parse_select_data) + 4) + parse_select_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, parse_select_msg)
    packets.append(c_packet)
    seq_num += len(parse_select_msg)

    # 10. Bind SELECT
    bind_select_data = b"select_portal\0select_stmt\0" + struct.pack("!HHH", 0, 0, 0)
    bind_select_msg = b"B" + struct.pack("!I", len(bind_select_data) + 4) + bind_select_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, bind_select_msg)
    packets.append(c_packet)
    seq_num += len(bind_select_msg)

    # 11. Execute SELECT
    exec_select_data = b"select_portal\0" + struct.pack("!I", 0)
    exec_select_msg = b"E" + struct.pack("!I", len(exec_select_data) + 4) + exec_select_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, exec_select_msg)
    packets.append(c_packet)
    seq_num += len(exec_select_msg)

    # 12. Parse COMMIT
    parse_commit_data = b"commit_stmt\0COMMIT;\0" + struct.pack("!H", 0)
    parse_commit_msg = b"P" + struct.pack("!I", len(parse_commit_data) + 4) + parse_commit_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, parse_commit_msg)
    packets.append(c_packet)
    seq_num += len(parse_commit_msg)

    # 13. Bind COMMIT
    bind_commit_data = b"commit_portal\0commit_stmt\0" + struct.pack("!HHH", 0, 0, 0)
    bind_commit_msg = b"B" + struct.pack("!I", len(bind_commit_data) + 4) + bind_commit_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, bind_commit_msg)
    packets.append(c_packet)
    seq_num += len(bind_commit_msg)

    # 14. Execute COMMIT
    exec_commit_data = b"commit_portal\0" + struct.pack("!I", 0)
    exec_commit_msg = b"E" + struct.pack("!I", len(exec_commit_data) + 4) + exec_commit_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, exec_commit_msg)
    packets.append(c_packet)
    seq_num += len(exec_commit_msg)

    # 15. 第二个Sync消息（结束第二个事务段）
    sync2_msg = b"S" + struct.pack("!I", 4)
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, sync2_msg)
    packets.append(c_packet)
    seq_num += len(sync2_msg)

    # 16. 服务器响应第二个事务段
    server_resp2 = b""
    server_resp2 += b"1" + struct.pack("!I", 4)  # ParseComplete for SELECT
    server_resp2 += b"2" + struct.pack("!I", 4)  # BindComplete for SELECT

    # SELECT结果
    row_desc_data = struct.pack("!H", 2)  # 2列
    row_desc_data += b"id\0" + struct.pack("!IHIhih", 0, 0, 23, 4, 4, 0)
    row_desc_data += b"name\0" + struct.pack("!IHIhih", 0, 0, 25, -1, -1, 0)
    row_desc = b"T" + struct.pack("!I", len(row_desc_data) + 4) + row_desc_data

    data_row_content = struct.pack("!H", 2)
    data_row_content += struct.pack("!I", 1) + b"1"
    data_row_content += struct.pack("!I", 4) + b"test"
    data_row = b"D" + struct.pack("!I", len(data_row_content) + 4) + data_row_content

    server_resp2 += row_desc + data_row
    # CommandComplete for SELECT - 动态计算长度
    select_cmd_text = b"SELECT 1\0"
    server_resp2 += b"C" + struct.pack("!I", len(select_cmd_text) + 4) + select_cmd_text
    server_resp2 += b"1" + struct.pack("!I", 4)  # ParseComplete for COMMIT
    server_resp2 += b"2" + struct.pack("!I", 4)  # BindComplete for COMMIT
    # CommandComplete for COMMIT - 动态计算长度
    commit_cmd_text = b"COMMIT\0"
    server_resp2 += b"C" + struct.pack("!I", len(commit_cmd_text) + 4) + commit_cmd_text
    server_resp2 += b"Z" + struct.pack("!IB", 5, 73)  # ReadyForQuery (I=空闲)

    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp2)
    packets.append(s_packet)
    ack_num += len(server_resp2)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "pgsql_sync_separated_transactions.pcap")

def generate_pipelined_error_handling_scenario():
    """生成管道化查询中错误处理场景的数据包"""
    packets = []
    global seq_num, ack_num

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # 1. 客户端发送多个Parse消息，其中第二个有语法错误
    # Parse消息1（正常）
    parse1_data = b"stmt1\0SELECT 1 AS normal_query;\0" + struct.pack("!H", 0)
    parse1_msg = b"P" + struct.pack("!I", len(parse1_data) + 4) + parse1_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, parse1_msg)
    packets.append(c_packet)
    seq_num += len(parse1_msg)

    # Parse消息2（语法错误）
    parse2_data = b"stmt2\0SELECT FROM invalid_syntax;\0" + struct.pack("!H", 0)
    parse2_msg = b"P" + struct.pack("!I", len(parse2_data) + 4) + parse2_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, parse2_msg)
    packets.append(c_packet)
    seq_num += len(parse2_msg)

    # Parse消息3（正常，但会被跳过）
    parse3_data = b"stmt3\0SELECT 3 AS skipped_query;\0" + struct.pack("!H", 0)
    parse3_msg = b"P" + struct.pack("!I", len(parse3_data) + 4) + parse3_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, parse3_msg)
    packets.append(c_packet)
    seq_num += len(parse3_msg)

    # 2. 客户端发送对应的Bind消息
    bind1_data = b"portal1\0stmt1\0" + struct.pack("!HHH", 0, 0, 0)
    bind1_msg = b"B" + struct.pack("!I", len(bind1_data) + 4) + bind1_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, bind1_msg)
    packets.append(c_packet)
    seq_num += len(bind1_msg)

    bind2_data = b"portal2\0stmt2\0" + struct.pack("!HHH", 0, 0, 0)
    bind2_msg = b"B" + struct.pack("!I", len(bind2_data) + 4) + bind2_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, bind2_msg)
    packets.append(c_packet)
    seq_num += len(bind2_msg)

    bind3_data = b"portal3\0stmt3\0" + struct.pack("!HHH", 0, 0, 0)
    bind3_msg = b"B" + struct.pack("!I", len(bind3_data) + 4) + bind3_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, bind3_msg)
    packets.append(c_packet)
    seq_num += len(bind3_msg)

    # 3. 客户端发送Sync消息
    sync_msg = b"S" + struct.pack("!I", 4)
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, sync_msg)
    packets.append(c_packet)
    seq_num += len(sync_msg)

    # 4. 服务器响应：第一个Parse成功，第二个Parse失败，后续消息被跳过
    server_resp = b""

    # 第一个Parse成功
    server_resp += b"1" + struct.pack("!I", 4)  # ParseComplete

    # 第二个Parse失败
    error_resp = b"E" + struct.pack("!I", 95)
    error_resp += b"SERROR\0"  # 严重性
    error_resp += b"C42601\0"  # 错误代码：syntax_error
    error_resp += b"Msyntax error at or near \"FROM\"\0"  # 错误消息
    error_resp += b"P8\0"  # 错误位置
    error_resp += b"Fparse.c\0"  # 文件名
    error_resp += b"L123\0"  # 行号
    error_resp += b"Rparser\0"  # 例程名
    error_resp += b"\0"  # 终止符
    server_resp += error_resp

    # 由于错误，后续消息被跳过，直接发送ReadyForQuery
    server_resp += b"Z" + struct.pack("!IB", 5, 73)  # ReadyForQuery (I=空闲)

    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp)
    packets.append(s_packet)
    ack_num += len(server_resp)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "pgsql_pipelined_error_handling.pcap", is_abnormal=True)

def generate_function_call_scenario():
    """生成函数调用协议场景的数据包"""
    packets = []
    global seq_num, ack_num

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # 1. 客户端发送FunctionCall消息
    # 调用内置函数 abs(-42)，函数OID为1397（abs函数的OID）
    function_oid = 1397  # abs函数的OID

    func_call_data = struct.pack("!I", function_oid)  # 函数OID
    func_call_data += struct.pack("!H", 1)  # 1个参数格式代码
    func_call_data += struct.pack("!H", 0)  # 参数格式：0=文本
    func_call_data += struct.pack("!H", 1)  # 1个参数

    # 参数：-42
    param_value = b"-42"
    func_call_data += struct.pack("!I", len(param_value)) + param_value

    func_call_data += struct.pack("!H", 0)  # 结果格式：0=文本

    func_call_msg = b"F" + struct.pack("!I", len(func_call_data) + 4) + func_call_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, func_call_msg)
    packets.append(c_packet)
    seq_num += len(func_call_msg)

    # 2. 服务器响应FunctionCallResponse
    result_value = b"42"  # abs(-42) = 42
    func_resp_data = struct.pack("!I", len(result_value)) + result_value
    func_resp = b"V" + struct.pack("!I", len(func_resp_data) + 4) + func_resp_data

    # ReadyForQuery
    ready = b"Z" + struct.pack("!IB", 5, 73)

    server_resp = func_resp + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp)
    packets.append(s_packet)
    ack_num += len(server_resp)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "pgsql_function_call.pcap")

def generate_function_call_error_scenario():
    """生成函数调用错误场景的数据包"""
    packets = []
    global seq_num, ack_num

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # 1. 客户端发送FunctionCall消息（调用不存在的函数）
    invalid_function_oid = 99999  # 不存在的函数OID

    func_call_data = struct.pack("!I", invalid_function_oid)  # 无效函数OID
    func_call_data += struct.pack("!H", 0)  # 0个参数格式代码
    func_call_data += struct.pack("!H", 0)  # 0个参数
    func_call_data += struct.pack("!H", 0)  # 结果格式：0=文本

    func_call_msg = b"F" + struct.pack("!I", len(func_call_data) + 4) + func_call_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, func_call_msg)
    packets.append(c_packet)
    seq_num += len(func_call_msg)

    # 2. 服务器响应ErrorResponse
    error_resp = b"E" + struct.pack("!I", 85)
    error_resp += b"SERROR\0"  # 严重性
    error_resp += b"C42883\0"  # 错误代码：undefined_function
    error_resp += b"Mfunction with OID 99999 does not exist\0"  # 错误消息
    error_resp += b"Ffmgr.c\0"  # 文件名
    error_resp += b"L456\0"  # 行号
    error_resp += b"RfmgrtabFindByOid\0"  # 例程名
    error_resp += b"\0"  # 终止符

    # ReadyForQuery
    ready = b"Z" + struct.pack("!IB", 5, 73)

    server_resp = error_resp + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp)
    packets.append(s_packet)
    ack_num += len(server_resp)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "pgsql_function_call_error.pcap", is_abnormal=True)

def generate_start_replication_scenario():
    """生成START_REPLICATION流复制协议场景的数据包

    测试目的：验证PostgreSQL流复制启动流程
    协议特性：START_REPLICATION命令 -> CopyBothResponse -> WAL数据流
    预期结果：成功启动流复制，建立双向数据流连接
    """
    packets = []
    global seq_num, ack_num

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # 1. 客户端发送START_REPLICATION命令
    # 参考：https://www.postgresql.org/docs/current/protocol-replication.html
    replication_sql = b"START_REPLICATION SLOT test_slot LOGICAL 0/0;\0"
    replication_query = b"Q" + struct.pack("!I", len(replication_sql) + 4) + replication_sql
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, replication_query)
    packets.append(c_packet)
    seq_num += len(replication_query)

    # 2. 服务器响应CopyBothResponse，建立双向流复制连接
    # CopyBothResponse格式：格式代码(1字节) + 列数(2字节) + 列格式代码(每列2字节)
    copy_both_data = struct.pack("!B", 0)  # 0=文本格式，1=二进制格式
    copy_both_data += struct.pack("!H", 0)  # 0列（流复制不使用列）
    copy_both_response = b"W" + struct.pack("!I", len(copy_both_data) + 4) + copy_both_data
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, copy_both_response)
    packets.append(s_packet)
    ack_num += len(copy_both_response)

    # 3. 服务器发送WAL数据流（模拟逻辑复制数据）
    # WAL消息格式：消息类型 + LSN + 时间戳 + 数据
    wal_data = b"w"  # 'w' = WAL数据消息类型
    wal_data += struct.pack("!Q", 0x1000000)  # 起始LSN (Log Sequence Number)
    wal_data += struct.pack("!Q", 0x1000100)  # 结束LSN
    wal_data += struct.pack("!Q", 1640995200000000)  # 时间戳（微秒）

    # 逻辑复制数据内容（简化的JSON格式）
    logical_data = b'{"change":[{"kind":"insert","schema":"public","table":"users","columnnames":["id","name"],"columnvalues":[1,"Alice"]}]}'
    wal_data += logical_data

    copy_data_wal = b"d" + struct.pack("!I", len(wal_data) + 4) + wal_data
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, copy_data_wal)
    packets.append(s_packet)
    ack_num += len(copy_data_wal)

    # 4. 客户端发送复制反馈消息
    # 复制反馈格式：消息类型 + 接收LSN + 刷新LSN + 应用LSN + 时间戳 + 回复标志
    feedback_data = b"r"  # 'r' = 复制反馈消息类型
    feedback_data += struct.pack("!Q", 0x1000100)  # 最后接收的LSN
    feedback_data += struct.pack("!Q", 0x1000100)  # 最后刷新的LSN
    feedback_data += struct.pack("!Q", 0x1000100)  # 最后应用的LSN
    feedback_data += struct.pack("!Q", 1640995200000000)  # 客户端时间戳
    feedback_data += struct.pack("!B", 0)  # 回复标志：0=不需要立即回复

    copy_data_feedback = b"d" + struct.pack("!I", len(feedback_data) + 4) + feedback_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, copy_data_feedback)
    packets.append(c_packet)
    seq_num += len(copy_data_feedback)

    # 5. 服务器发送Keep-Alive消息
    # Keep-Alive格式：消息类型 + 服务器LSN + 时间戳 + 回复请求标志
    keepalive_data = b"k"  # 'k' = Keep-Alive消息类型
    keepalive_data += struct.pack("!Q", 0x1000100)  # 服务器当前LSN
    keepalive_data += struct.pack("!Q", 1640995201000000)  # 服务器时间戳
    keepalive_data += struct.pack("!B", 1)  # 回复请求标志：1=请求客户端回复

    copy_data_keepalive = b"d" + struct.pack("!I", len(keepalive_data) + 4) + keepalive_data
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, copy_data_keepalive)
    packets.append(s_packet)
    ack_num += len(copy_data_keepalive)

    # 6. 客户端响应Keep-Alive
    keepalive_response_data = b"r"  # 复制反馈消息
    keepalive_response_data += struct.pack("!Q", 0x1000100)  # 接收LSN
    keepalive_response_data += struct.pack("!Q", 0x1000100)  # 刷新LSN
    keepalive_response_data += struct.pack("!Q", 0x1000100)  # 应用LSN
    keepalive_response_data += struct.pack("!Q", 1640995201000000)  # 客户端时间戳
    keepalive_response_data += struct.pack("!B", 0)  # 回复标志

    copy_data_response = b"d" + struct.pack("!I", len(keepalive_response_data) + 4) + keepalive_response_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, copy_data_response)
    packets.append(c_packet)
    seq_num += len(copy_data_response)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "pgsql_start_replication.pcap")

def generate_wal_streaming_scenario():
    """生成WAL数据流传输场景的数据包

    测试目的：验证WAL数据流的连续传输和处理
    协议特性：连续的WAL数据包传输，包含多种操作类型
    预期结果：模拟真实的WAL数据流，包含INSERT、UPDATE、DELETE操作
    """
    packets = []
    global seq_num, ack_num

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # 假设已经建立了流复制连接，直接发送WAL数据

    # 1. 服务器发送INSERT操作的WAL数据
    insert_wal_data = b"w"  # WAL数据消息
    insert_wal_data += struct.pack("!Q", 0x2000000)  # 起始LSN
    insert_wal_data += struct.pack("!Q", 0x2000200)  # 结束LSN
    insert_wal_data += struct.pack("!Q", 1640995300000000)  # 时间戳

    # 逻辑复制INSERT数据
    insert_logical_data = b'{"change":[{"kind":"insert","schema":"public","table":"products","columnnames":["id","name","price"],"columnvalues":[101,"Laptop",999.99]}]}'
    insert_wal_data += insert_logical_data

    copy_data_insert = b"d" + struct.pack("!I", len(insert_wal_data) + 4) + insert_wal_data
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, copy_data_insert)
    packets.append(s_packet)
    ack_num += len(copy_data_insert)

    # 2. 服务器发送UPDATE操作的WAL数据
    update_wal_data = b"w"  # WAL数据消息
    update_wal_data += struct.pack("!Q", 0x2000200)  # 起始LSN
    update_wal_data += struct.pack("!Q", 0x2000400)  # 结束LSN
    update_wal_data += struct.pack("!Q", 1640995400000000)  # 时间戳

    # 逻辑复制UPDATE数据
    update_logical_data = b'{"change":[{"kind":"update","schema":"public","table":"products","columnnames":["id","name","price"],"columnvalues":[101,"Gaming Laptop",1299.99],"oldkeys":{"keynames":["id"],"keyvalues":[101]}}]}'
    update_wal_data += update_logical_data

    copy_data_update = b"d" + struct.pack("!I", len(update_wal_data) + 4) + update_wal_data
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, copy_data_update)
    packets.append(s_packet)
    ack_num += len(copy_data_update)

    # 3. 客户端发送进度反馈
    progress_feedback_data = b"r"  # 复制反馈
    progress_feedback_data += struct.pack("!Q", 0x2000400)  # 接收LSN
    progress_feedback_data += struct.pack("!Q", 0x2000400)  # 刷新LSN
    progress_feedback_data += struct.pack("!Q", 0x2000200)  # 应用LSN（稍微滞后）
    progress_feedback_data += struct.pack("!Q", 1640995400000000)  # 时间戳
    progress_feedback_data += struct.pack("!B", 0)  # 回复标志

    copy_data_progress = b"d" + struct.pack("!I", len(progress_feedback_data) + 4) + progress_feedback_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, copy_data_progress)
    packets.append(c_packet)
    seq_num += len(copy_data_progress)

    # 4. 服务器发送DELETE操作的WAL数据
    delete_wal_data = b"w"  # WAL数据消息
    delete_wal_data += struct.pack("!Q", 0x2000400)  # 起始LSN
    delete_wal_data += struct.pack("!Q", 0x2000600)  # 结束LSN
    delete_wal_data += struct.pack("!Q", 1640995500000000)  # 时间戳

    # 逻辑复制DELETE数据
    delete_logical_data = b'{"change":[{"kind":"delete","schema":"public","table":"products","oldkeys":{"keynames":["id"],"keyvalues":[101]}}]}'
    delete_wal_data += delete_logical_data

    copy_data_delete = b"d" + struct.pack("!I", len(delete_wal_data) + 4) + delete_wal_data
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, copy_data_delete)
    packets.append(s_packet)
    ack_num += len(copy_data_delete)

    # 5. 客户端发送最终确认
    final_feedback_data = b"r"  # 复制反馈
    final_feedback_data += struct.pack("!Q", 0x2000600)  # 接收LSN
    final_feedback_data += struct.pack("!Q", 0x2000600)  # 刷新LSN
    final_feedback_data += struct.pack("!Q", 0x2000600)  # 应用LSN
    final_feedback_data += struct.pack("!Q", 1640995500000000)  # 时间戳
    final_feedback_data += struct.pack("!B", 0)  # 回复标志

    copy_data_final = b"d" + struct.pack("!I", len(final_feedback_data) + 4) + final_feedback_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, copy_data_final)
    packets.append(c_packet)
    seq_num += len(copy_data_final)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "pgsql_wal_streaming.pcap")

def generate_replication_feedback_scenario():
    """生成复制反馈消息场景的数据包

    测试目的：验证复制进度反馈和状态同步机制
    协议特性：复制反馈消息的各种状态和时间戳处理
    预期结果：正确的复制进度跟踪和反馈机制
    """
    packets = []
    global seq_num, ack_num

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # 假设已经建立了流复制连接

    # 1. 客户端发送初始状态反馈
    initial_feedback_data = b"r"  # 复制反馈消息类型
    initial_feedback_data += struct.pack("!Q", 0x3000000)  # 最后接收的LSN
    initial_feedback_data += struct.pack("!Q", 0x2FFF000)  # 最后刷新的LSN（稍微滞后）
    initial_feedback_data += struct.pack("!Q", 0x2FFE000)  # 最后应用的LSN（更滞后）
    initial_feedback_data += struct.pack("!Q", 1640995600000000)  # 客户端时间戳
    initial_feedback_data += struct.pack("!B", 0)  # 回复标志：不需要立即回复

    copy_data_initial = b"d" + struct.pack("!I", len(initial_feedback_data) + 4) + initial_feedback_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, copy_data_initial)
    packets.append(c_packet)
    seq_num += len(copy_data_initial)

    # 2. 服务器发送状态查询Keep-Alive
    status_keepalive_data = b"k"  # Keep-Alive消息类型
    status_keepalive_data += struct.pack("!Q", 0x3000500)  # 服务器当前LSN
    status_keepalive_data += struct.pack("!Q", 1640995700000000)  # 服务器时间戳
    status_keepalive_data += struct.pack("!B", 1)  # 回复请求标志：请求客户端回复状态

    copy_data_status = b"d" + struct.pack("!I", len(status_keepalive_data) + 4) + status_keepalive_data
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, copy_data_status)
    packets.append(s_packet)
    ack_num += len(copy_data_status)

    # 3. 客户端发送更新的进度反馈
    updated_feedback_data = b"r"  # 复制反馈消息类型
    updated_feedback_data += struct.pack("!Q", 0x3000500)  # 最后接收的LSN（已更新）
    updated_feedback_data += struct.pack("!Q", 0x3000200)  # 最后刷新的LSN（追赶中）
    updated_feedback_data += struct.pack("!Q", 0x3000000)  # 最后应用的LSN（继续追赶）
    updated_feedback_data += struct.pack("!Q", 1640995700000000)  # 客户端时间戳
    updated_feedback_data += struct.pack("!B", 0)  # 回复标志

    copy_data_updated = b"d" + struct.pack("!I", len(updated_feedback_data) + 4) + updated_feedback_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, copy_data_updated)
    packets.append(c_packet)
    seq_num += len(copy_data_updated)

    # 4. 客户端发送同步完成反馈
    sync_complete_feedback_data = b"r"  # 复制反馈消息类型
    sync_complete_feedback_data += struct.pack("!Q", 0x3000500)  # 最后接收的LSN
    sync_complete_feedback_data += struct.pack("!Q", 0x3000500)  # 最后刷新的LSN（已同步）
    sync_complete_feedback_data += struct.pack("!Q", 0x3000500)  # 最后应用的LSN（完全同步）
    sync_complete_feedback_data += struct.pack("!Q", 1640995800000000)  # 客户端时间戳
    sync_complete_feedback_data += struct.pack("!B", 0)  # 回复标志

    copy_data_sync = b"d" + struct.pack("!I", len(sync_complete_feedback_data) + 4) + sync_complete_feedback_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, copy_data_sync)
    packets.append(c_packet)
    seq_num += len(copy_data_sync)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "pgsql_replication_feedback.pcap")

def generate_ssl_request_supported_scenario():
    """生成SSL连接请求支持场景的数据包

    测试目的：验证服务器支持SSL连接的协商流程
    协议特性：SSLRequest消息 -> 服务器返回'S'表示支持SSL
    预期结果：成功协商SSL连接，为后续TLS握手做准备
    """
    packets = []
    global seq_num, ack_num

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # 1. 客户端发送SSLRequest消息
    # SSLRequest格式：长度(4字节) + SSL请求代码(4字节)
    # SSL请求代码：80877103 (0x04D2162F)
    ssl_request_data = struct.pack("!I", 80877103)  # SSL请求代码
    ssl_request_msg = struct.pack("!I", len(ssl_request_data) + 4) + ssl_request_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, ssl_request_msg)
    packets.append(c_packet)
    seq_num += len(ssl_request_msg)

    # 2. 服务器响应支持SSL（返回'S'）
    ssl_supported_response = b"S"  # 单字节响应：'S'表示支持SSL
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, ssl_supported_response)
    packets.append(s_packet)
    ack_num += len(ssl_supported_response)

    # 注意：在真实场景中，此时会开始TLS握手，但这超出了PostgreSQL协议范围
    # 这里我们模拟TLS握手的开始（ClientHello消息的开头）
    # 实际的TLS握手由TLS协议处理，不是PostgreSQL协议的一部分

    # 3. 模拟TLS ClientHello消息开始（仅作为示例，实际由TLS层处理）
    # TLS记录头：类型(1) + 版本(2) + 长度(2) + 握手类型(1) + 握手长度(3) + 协议版本(2)
    tls_client_hello_start = bytes([
        0x16,  # TLS记录类型：握手
        0x03, 0x03,  # TLS版本：1.2
        0x00, 0x40,  # 记录长度：64字节（示例）
        0x01,  # 握手类型：ClientHello
        0x00, 0x00, 0x3C,  # 握手消息长度：60字节
        0x03, 0x03,  # 协议版本：TLS 1.2
    ])

    # 添加随机数（32字节，简化为固定值）
    tls_random = bytes([0x01] * 32)
    tls_client_hello_start += tls_random

    # 会话ID长度（1字节）+ 会话ID（0字节）
    tls_client_hello_start += bytes([0x00])

    # 密码套件长度（2字节）+ 密码套件（示例）
    tls_client_hello_start += bytes([0x00, 0x02, 0x00, 0x2F])  # TLS_RSA_WITH_AES_128_CBC_SHA

    # 压缩方法长度（1字节）+ 压缩方法（1字节）
    tls_client_hello_start += bytes([0x01, 0x00])  # 无压缩

    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, tls_client_hello_start)
    packets.append(c_packet)
    seq_num += len(tls_client_hello_start)

    # 注意：这里不继续TLS握手，因为这是TLS协议的内容，不是PostgreSQL协议

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "pgsql_ssl_request_supported.pcap")

def generate_ssl_request_not_supported_scenario():
    """生成SSL连接请求不支持场景的数据包

    测试目的：验证服务器不支持SSL连接时的处理流程
    协议特性：SSLRequest消息 -> 服务器返回'N'表示不支持SSL
    预期结果：服务器拒绝SSL连接，客户端应回退到非加密连接
    """
    packets = []
    global seq_num, ack_num

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # 1. 客户端发送SSLRequest消息
    ssl_request_data = struct.pack("!I", 80877103)  # SSL请求代码
    ssl_request_msg = struct.pack("!I", len(ssl_request_data) + 4) + ssl_request_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, ssl_request_msg)
    packets.append(c_packet)
    seq_num += len(ssl_request_msg)

    # 2. 服务器响应不支持SSL（返回'N'）
    ssl_not_supported_response = b"N"  # 单字节响应：'N'表示不支持SSL
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, ssl_not_supported_response)
    packets.append(s_packet)
    ack_num += len(ssl_not_supported_response)

    # 3. 客户端回退到普通连接，发送StartupMessage
    startup_data = b"user\0testuser\0database\0testdb\0application_name\0test_app\0\0"
    startup_msg = struct.pack("!II", len(startup_data) + 8, 196608) + startup_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, startup_msg)
    packets.append(c_packet)
    seq_num += len(startup_msg)

    # 4. 服务器要求认证
    auth_req = b"R" + struct.pack("!II", 12, 5) + b"salt"  # MD5认证
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, auth_req)
    packets.append(s_packet)
    ack_num += len(auth_req)

    # 5. 客户端发送密码响应
    password_data = b"md5hashedpassword123456789abcdef0123"
    password_resp = b"p" + struct.pack("!I", len(password_data) + 5) + password_data + b"\0"
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, password_resp)
    packets.append(c_packet)
    seq_num += len(password_resp)

    # 6. 服务器认证成功并准备就绪
    auth_ok_content = struct.pack("!I", 0)  # 认证成功标识
    auth_ok = b"R" + struct.pack("!I", len(auth_ok_content) + 4) + auth_ok_content
    backend_key_content = struct.pack("!II", 12345, 67890)
    backend_key = b"K" + struct.pack("!I", len(backend_key_content) + 4) + backend_key_content
    ready_content = struct.pack("!B", 73)  # 73 = 'I' (空闲状态)
    ready = b"Z" + struct.pack("!I", len(ready_content) + 4) + ready_content

    server_resp = auth_ok + backend_key + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp)
    packets.append(s_packet)
    ack_num += len(server_resp)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "pgsql_ssl_request_not_supported.pcap")

def generate_max_message_length_scenario():
    """生成最大消息长度测试场景的数据包

    测试目的：验证协议解析器对最大消息长度的处理能力
    协议特性：接近PostgreSQL协议最大消息长度限制的查询
    预期结果：正确处理大消息，不出现缓冲区溢出或截断
    """
    packets = []
    global seq_num, ack_num

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # PostgreSQL协议消息最大长度约为1GB（2^30字节），但实际测试中使用较小的值
    # 考虑到TCP包大小限制，使用更小的测试值
    max_test_size = 16384  # 16KB

    # 1. 客户端发送大型查询消息
    # 构造一个包含大量数据的SELECT语句
    base_query = "SELECT "

    # 生成大量的列选择，每个列约100字节
    columns = []
    for i in range(150):  # 150列 * 100字节 ≈ 15KB
        column_expr = f"'{i:04d}_" + "x" * 90 + f"' AS col_{i:04d}"
        columns.append(column_expr)

    large_query = base_query + ", ".join(columns) + ";\0"
    large_query_bytes = large_query.encode()

    # 确保不超过我们的测试限制
    if len(large_query_bytes) > max_test_size:
        # 如果太大，截断到合适大小
        truncate_size = max_test_size - 100  # 留一些余量
        large_query_bytes = large_query_bytes[:truncate_size] + b";\0"

    query_msg = b"Q" + struct.pack("!I", len(large_query_bytes) + 4) + large_query_bytes
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, query_msg)
    packets.append(c_packet)
    seq_num += len(query_msg)

    # 2. 服务器响应大型RowDescription
    # 构造包含大量列的行描述
    actual_columns = len(columns) if len(large_query_bytes) < max_test_size else 120  # 估算实际列数
    row_desc_data = struct.pack("!H", actual_columns)

    for i in range(actual_columns):
        col_name = f"col_{i:04d}\0".encode()
        row_desc_data += col_name + struct.pack("!IHIhih", 0, 0, 25, -1, -1, 0)  # text类型

    row_desc = b"T" + struct.pack("!I", len(row_desc_data) + 4) + row_desc_data
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, row_desc)
    packets.append(s_packet)
    ack_num += len(row_desc)

    # 3. 服务器发送大型DataRow
    data_row_content = struct.pack("!H", actual_columns)
    for i in range(actual_columns):
        col_value = f"{i:04d}_" + "x" * 90  # 与查询中的值对应
        col_value_bytes = col_value.encode()
        data_row_content += struct.pack("!I", len(col_value_bytes)) + col_value_bytes

    data_row = b"D" + struct.pack("!I", len(data_row_content) + 4) + data_row_content
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, data_row)
    packets.append(s_packet)
    ack_num += len(data_row)

    # 4. CommandComplete和ReadyForQuery
    cmd_complete_text = b"SELECT 1\0"
    cmd_complete = b"C" + struct.pack("!I", len(cmd_complete_text) + 4) + cmd_complete_text
    ready = b"Z" + struct.pack("!IB", 5, 73)

    server_resp = cmd_complete + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp)
    packets.append(s_packet)
    ack_num += len(server_resp)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "pgsql_max_message_length.pcap")

def generate_max_parameters_scenario():
    """生成最大参数数量测试场景的数据包

    测试目的：验证预处理语句支持的最大参数数量
    协议特性：包含大量参数的预处理语句Parse/Bind/Execute流程
    预期结果：正确处理大量参数，验证参数数组边界处理
    """
    packets = []
    global seq_num, ack_num

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # PostgreSQL理论上支持65535个参数，但实际测试中使用较小的数量
    max_params = 1000  # 1000个参数

    # 1. 客户端发送包含大量参数的Parse消息
    # 构造INSERT语句，包含大量参数
    param_placeholders = ", ".join([f"${i+1}" for i in range(max_params)])
    sql_statement = f"INSERT INTO test_table (id, data) VALUES {param_placeholders};\0"
    sql_bytes = sql_statement.encode()

    # 参数类型数组
    param_types_data = struct.pack("!H", max_params)  # 参数数量
    for i in range(max_params):
        if i % 2 == 0:
            param_types_data += struct.pack("!I", 23)  # int4
        else:
            param_types_data += struct.pack("!I", 25)  # text

    parse_data = b"max_params_stmt\0" + sql_bytes + param_types_data
    parse_msg = b"P" + struct.pack("!I", len(parse_data) + 4) + parse_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, parse_msg)
    packets.append(c_packet)
    seq_num += len(parse_msg)

    # 2. 客户端发送包含大量参数值的Bind消息
    bind_data = b"max_params_portal\0max_params_stmt\0"
    bind_data += struct.pack("!H", 0)  # 参数格式代码数量（使用默认）
    bind_data += struct.pack("!H", max_params)  # 参数值数量

    # 添加参数值
    for i in range(max_params):
        if i % 2 == 0:
            # 整数参数
            param_value = str(i).encode()
            bind_data += struct.pack("!I", len(param_value)) + param_value
        else:
            # 文本参数
            param_value = f"text_value_{i}".encode()
            bind_data += struct.pack("!I", len(param_value)) + param_value

    bind_data += struct.pack("!H", 0)  # 结果格式代码数量（使用默认）

    bind_msg = b"B" + struct.pack("!I", len(bind_data) + 4) + bind_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, bind_msg)
    packets.append(c_packet)
    seq_num += len(bind_msg)

    # 3. 客户端发送Execute消息
    execute_data = b"max_params_portal\0" + struct.pack("!I", 0)
    execute_msg = b"E" + struct.pack("!I", len(execute_data) + 4) + execute_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, execute_msg)
    packets.append(c_packet)
    seq_num += len(execute_msg)

    # 4. 客户端发送Sync消息
    sync_msg = b"S" + struct.pack("!I", 4)
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, sync_msg)
    packets.append(c_packet)
    seq_num += len(sync_msg)

    # 5. 服务器响应
    parse_complete = b"1" + struct.pack("!I", 4)
    bind_complete = b"2" + struct.pack("!I", 4)

    cmd_complete_text = f"INSERT 0 {max_params//2}\0".encode()  # 假设插入了一半的行
    cmd_complete = b"C" + struct.pack("!I", len(cmd_complete_text) + 4) + cmd_complete_text

    ready = b"Z" + struct.pack("!IB", 5, 73)

    server_resp = parse_complete + bind_complete + cmd_complete + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp)
    packets.append(s_packet)
    ack_num += len(server_resp)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "pgsql_max_parameters.pcap")

def generate_max_string_length_scenario():
    """生成最大字符串长度测试场景的数据包

    测试目的：验证协议解析器对最大字符串字段长度的处理
    协议特性：包含超长字符串的查询和响应
    预期结果：正确处理长字符串，验证字符串长度字段的边界处理
    """
    packets = []
    global seq_num, ack_num

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # PostgreSQL单个字段最大长度约1GB，但测试中使用较小值
    # 考虑到TCP包大小限制，使用更小的测试值
    max_string_length = 8192  # 8KB字符串

    # 1. 客户端发送包含超长字符串的查询
    # 构造包含长字符串字面量的SELECT语句
    long_string = "x" * max_string_length
    sql_statement = f"SELECT '{long_string}' AS long_text, length('{long_string}') AS text_length;\0"
    sql_bytes = sql_statement.encode()

    query_msg = b"Q" + struct.pack("!I", len(sql_bytes) + 4) + sql_bytes
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, query_msg)
    packets.append(c_packet)
    seq_num += len(query_msg)

    # 2. 服务器响应RowDescription
    row_desc_data = struct.pack("!H", 2)  # 2列
    # long_text列
    row_desc_data += b"long_text\0" + struct.pack("!IHIhih", 0, 0, 25, -1, -1, 0)
    # text_length列
    row_desc_data += b"text_length\0" + struct.pack("!IHIhih", 0, 0, 23, 4, 4, 0)

    row_desc = b"T" + struct.pack("!I", len(row_desc_data) + 4) + row_desc_data
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, row_desc)
    packets.append(s_packet)
    ack_num += len(row_desc)

    # 3. 服务器发送包含超长字符串的DataRow
    data_row_content = struct.pack("!H", 2)  # 2列

    # 第一列：超长字符串
    long_string_bytes = long_string.encode()
    data_row_content += struct.pack("!I", len(long_string_bytes)) + long_string_bytes

    # 第二列：字符串长度
    length_value = str(max_string_length).encode()
    data_row_content += struct.pack("!I", len(length_value)) + length_value

    data_row = b"D" + struct.pack("!I", len(data_row_content) + 4) + data_row_content
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, data_row)
    packets.append(s_packet)
    ack_num += len(data_row)

    # 4. CommandComplete和ReadyForQuery
    cmd_complete_text = b"SELECT 1\0"
    cmd_complete = b"C" + struct.pack("!I", len(cmd_complete_text) + 4) + cmd_complete_text
    ready = b"Z" + struct.pack("!IB", 5, 73)

    server_resp = cmd_complete + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp)
    packets.append(s_packet)
    ack_num += len(server_resp)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "pgsql_max_string_length.pcap")

def generate_empty_data_handling_scenario():
    """生成空数据处理测试场景的数据包

    测试目的：验证各种空值和零长度数据的正确处理
    协议特性：NULL值、空字符串、零长度字段的协议表示
    预期结果：正确区分和处理各种空数据情况
    """
    packets = []
    global seq_num, ack_num

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # 1. 客户端发送包含各种空值的查询
    sql_statement = b"SELECT NULL AS null_value, '' AS empty_string, 0 AS zero_number, '   ' AS whitespace_string, ARRAY[]::int[] AS empty_array;\0"
    query_msg = b"Q" + struct.pack("!I", len(sql_statement) + 4) + sql_statement
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, query_msg)
    packets.append(c_packet)
    seq_num += len(query_msg)

    # 2. 服务器响应RowDescription
    row_desc_data = struct.pack("!H", 5)  # 5列

    # null_value列
    row_desc_data += b"null_value\0" + struct.pack("!IHIhih", 0, 0, 25, -1, -1, 0)
    # empty_string列
    row_desc_data += b"empty_string\0" + struct.pack("!IHIhih", 0, 0, 25, -1, -1, 0)
    # zero_number列
    row_desc_data += b"zero_number\0" + struct.pack("!IHIhih", 0, 0, 23, 4, 4, 0)
    # whitespace_string列
    row_desc_data += b"whitespace_string\0" + struct.pack("!IHIhih", 0, 0, 25, -1, -1, 0)
    # empty_array列
    row_desc_data += b"empty_array\0" + struct.pack("!IHIhih", 0, 0, 1007, -1, -1, 0)

    row_desc = b"T" + struct.pack("!I", len(row_desc_data) + 4) + row_desc_data
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, row_desc)
    packets.append(s_packet)
    ack_num += len(row_desc)

    # 3. 服务器发送包含各种空值的DataRow
    data_row_content = struct.pack("!H", 5)  # 5列

    # 第一列：NULL值（使用-1表示NULL）
    data_row_content += struct.pack("!i", -1)

    # 第二列：空字符串（长度为0）
    data_row_content += struct.pack("!I", 0)  # 长度为0，无数据

    # 第三列：数字0
    zero_value = b"0"
    data_row_content += struct.pack("!I", len(zero_value)) + zero_value

    # 第四列：空白字符串
    whitespace_value = b"   "
    data_row_content += struct.pack("!I", len(whitespace_value)) + whitespace_value

    # 第五列：空数组
    empty_array_value = b"{}"
    data_row_content += struct.pack("!I", len(empty_array_value)) + empty_array_value

    data_row = b"D" + struct.pack("!I", len(data_row_content) + 4) + data_row_content
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, data_row)
    packets.append(s_packet)
    ack_num += len(data_row)

    # 4. 测试空查询响应
    # 客户端发送空查询
    empty_query = b"Q" + struct.pack("!I", 5) + b"\0"
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, empty_query)
    packets.append(c_packet)
    seq_num += len(empty_query)

    # 服务器响应EmptyQueryResponse
    empty_query_response = b"I" + struct.pack("!I", 4)  # EmptyQueryResponse消息

    # 第一个查询的CommandComplete和ReadyForQuery
    cmd_complete_text = b"SELECT 1\0"
    cmd_complete = b"C" + struct.pack("!I", len(cmd_complete_text) + 4) + cmd_complete_text
    ready1 = b"Z" + struct.pack("!IB", 5, 73)

    # 空查询的ReadyForQuery
    ready2 = b"Z" + struct.pack("!IB", 5, 73)

    server_resp = cmd_complete + ready1 + empty_query_response + ready2
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp)
    packets.append(s_packet)
    ack_num += len(server_resp)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "pgsql_empty_data_handling.pcap")

def main():
    """主函数，生成所有场景的PCAP文件"""
    print("开始生成PostgreSQL协议PCAP文件...")

    # 生成各种场景的PCAP
    # 原有场景
    generate_auth_scenario()              # MD5认证场景
    generate_auth_with_copy_scenario()  # 认证和COPY组合场景
    generate_simple_query_scenario()    # 简单查询场景
    generate_prepared_statement_scenario()  # 预处理语句场景
    generate_error_scenario()             # 错误场景
    generate_transaction_scenario()     # 事务场景
    generate_notification_scenario()    # 通知场景
    generate_large_result_scenario()    # 大型结果集场景
    generate_batch_operations_scenario()  # 批量操作场景
    generate_cancel_query_scenario()      # 取消查询场景
    generate_multi_query_scenario()       # 多查询场景
    generate_binary_extended_query_scenario()  # 二进制扩展查询场景
    # generate_high_performance_scenario()  # 高性能测试场景（暂时注释掉，太耗时）

    # 新增认证方式测试场景
    generate_cleartext_auth_scenario()   # 明文密码认证场景
    generate_auth_failure_scenario()     # 认证失败场景
    generate_user_not_exist_scenario()   # 用户不存在场景
    generate_sasl_auth_scenario()        # SASL认证场景

    # 新增扩展查询协议测试场景
    generate_describe_statement_scenario()  # Describe预处理语句场景
    generate_describe_portal_scenario()     # Describe门户场景
    generate_close_statement_portal_scenario()  # Close预处理语句和门户场景
    generate_flush_scenario()               # Flush消息场景
    generate_multi_parameter_scenario()     # 多参数预处理语句场景
    generate_nodata_scenario()              # NoData响应场景

    # 新增COPY操作完善测试场景
    generate_copy_to_stdout_scenario()      # COPY TO STDOUT操作场景
    generate_copy_fail_scenario()           # COPY操作错误处理场景
    generate_binary_copy_scenario()         # 二进制格式COPY操作场景

    # 新增错误处理完善测试场景
    generate_syntax_error_scenario()        # SQL语法错误场景
    generate_type_error_scenario()          # 数据类型错误场景
    generate_constraint_violation_scenario() # 约束冲突错误场景
    generate_fatal_error_scenario()         # FATAL级别错误场景
    generate_panic_error_scenario()         # PANIC级别错误场景
    generate_notice_response_scenario()     # NoticeResponse消息场景

    # 新增数据类型测试场景
    generate_multiple_data_types_scenario() # 多种数据类型测试场景
    generate_character_encoding_scenario()  # 字符编码测试场景

    # 新增管道化查询测试场景
    generate_pipelined_queries_scenario()   # 管道化查询测试场景
    generate_sync_separated_transactions_scenario() # Sync消息分隔事务段场景
    generate_pipelined_error_handling_scenario() # 管道中的错误处理场景

    # 新增函数调用协议测试场景
    generate_function_call_scenario()       # 函数调用协议场景
    generate_function_call_error_scenario() # 函数调用错误场景

    # 新增流复制协议测试场景
    generate_start_replication_scenario()   # START_REPLICATION流复制启动场景
    generate_wal_streaming_scenario()       # WAL数据流传输场景
    generate_replication_feedback_scenario() # 复制反馈消息场景

    # 新增SSL/TLS连接测试场景
    generate_ssl_request_supported_scenario()     # SSL连接请求支持场景
    generate_ssl_request_not_supported_scenario() # SSL连接请求不支持场景

    # 新增边界条件测试场景
    generate_max_message_length_scenario()  # 最大消息长度测试场景
    generate_max_parameters_scenario()      # 最大参数数量测试场景
    generate_max_string_length_scenario()   # 最大字符串长度测试场景
    generate_empty_data_handling_scenario() # 空数据处理测试场景

    print(f"正常测试场景PCAP文件已生成到 {output_dir} 目录")
    print(f"异常测试场景PCAP文件已生成到 {abnormal_output_dir} 目录")

if __name__ == "__main__":
    # 创建输出目录
    output_dir = "pgsql_pcaps"
    abnormal_output_dir = "pgsql_abnormal_pcaps"

    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    if not os.path.exists(abnormal_output_dir):
        os.makedirs(abnormal_output_dir)

    main()