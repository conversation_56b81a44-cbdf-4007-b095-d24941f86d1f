#!/usr/bin/env python3
"""
验证PostgreSQL协议修复的脚本
检查修复后的三个关键函数生成的PCAP文件中的协议格式
"""

import struct
from scapy.all import rdpcap, TCP

def validate_postgresql_message(data, offset=0):
    """验证PostgreSQL协议消息格式"""
    if len(data) < offset + 5:
        return False, "数据太短"

    msg_type = chr(data[offset])
    msg_length = struct.unpack("!I", data[offset+1:offset+5])[0]
    expected_total_length = offset + 1 + msg_length

    if len(data) < expected_total_length:
        return False, f"长度不匹配: 声明{msg_length}, 实际不足"

    return True, f"消息'{msg_type}'格式正确，长度{msg_length}"

def extract_postgresql_data(pcap_file):
    """从PCAP文件中提取PostgreSQL协议数据"""
    packets = rdpcap(pcap_file)
    pgsql_data = []
    
    for packet in packets:
        if TCP in packet and packet[TCP].dport == 5432 or (TCP in packet and packet[TCP].sport == 5432):
            if hasattr(packet[TCP], 'payload') and len(packet[TCP].payload) > 0:
                payload = bytes(packet[TCP].payload)
                if len(payload) > 0:
                    pgsql_data.append(payload)
    
    return pgsql_data

def verify_large_result_scenario():
    """验证大结果集场景的修复"""
    print("=== 验证 pgsql_large_result.pcap ===")
    try:
        data_list = extract_postgresql_data("pgsql_pcaps/pgsql_large_result.pcap")
        
        for i, data in enumerate(data_list):
            print(f"数据包 {i+1}: 长度 {len(data)} 字节")
            
            offset = 0
            msg_count = 0
            while offset < len(data):
                if offset + 5 > len(data):
                    break
                    
                is_valid, msg = validate_postgresql_message(data, offset)
                if is_valid:
                    msg_count += 1
                    print(f"  消息 {msg_count}: {msg}")
                    
                    # 获取消息长度并移动到下一个消息
                    msg_length = struct.unpack("!I", data[offset+1:offset+5])[0]
                    offset += 1 + msg_length
                else:
                    print(f"  错误: {msg}")
                    break
            
            if msg_count > 0:
                print(f"  ✓ 成功解析 {msg_count} 个PostgreSQL消息")
            else:
                print(f"  ✗ 未找到有效的PostgreSQL消息")
            print()
            
    except Exception as e:
        print(f"验证失败: {e}")

def verify_prepared_statement_scenario():
    """验证预处理语句场景的修复"""
    print("=== 验证 pgsql_prepared_statement.pcap ===")
    try:
        data_list = extract_postgresql_data("pgsql_pcaps/pgsql_prepared_statement.pcap")
        
        for i, data in enumerate(data_list):
            print(f"数据包 {i+1}: 长度 {len(data)} 字节")
            
            offset = 0
            msg_count = 0
            while offset < len(data):
                if offset + 5 > len(data):
                    break
                    
                is_valid, msg = validate_postgresql_message(data, offset)
                if is_valid:
                    msg_count += 1
                    print(f"  消息 {msg_count}: {msg}")
                    
                    # 检查DataRow消息中的email字段长度
                    if data[offset] == ord('D'):  # DataRow消息
                        msg_length = struct.unpack("!I", data[offset+1:offset+5])[0]
                        row_data = data[offset+5:offset+1+msg_length]
                        if len(row_data) >= 2:
                            field_count = struct.unpack("!H", row_data[0:2])[0]
                            print(f"    DataRow包含 {field_count} 个字段")
                            
                            # 检查email字段（第3个字段）
                            field_offset = 2
                            for field_idx in range(field_count):
                                if field_offset + 4 <= len(row_data):
                                    field_len = struct.unpack("!I", row_data[field_offset:field_offset+4])[0]
                                    field_data = row_data[field_offset+4:field_offset+4+field_len]
                                    if field_idx == 2:  # email字段
                                        print(f"    Email字段: 长度={field_len}, 内容='{field_data.decode()}'")
                                        if field_data == b"<EMAIL>":
                                            print(f"    ✓ Email字段长度计算正确: {len(field_data)} 字节")
                                        else:
                                            print(f"    ✗ Email字段内容不匹配")
                                    field_offset += 4 + field_len
                    
                    # 获取消息长度并移动到下一个消息
                    msg_length = struct.unpack("!I", data[offset+1:offset+5])[0]
                    offset += 1 + msg_length
                else:
                    print(f"  错误: {msg}")
                    break
            
            if msg_count > 0:
                print(f"  ✓ 成功解析 {msg_count} 个PostgreSQL消息")
            else:
                print(f"  ✗ 未找到有效的PostgreSQL消息")
            print()
            
    except Exception as e:
        print(f"验证失败: {e}")

def verify_sync_separated_transactions_scenario():
    """验证Sync分隔事务场景的修复"""
    print("=== 验证 pgsql_sync_separated_transactions.pcap ===")
    try:
        data_list = extract_postgresql_data("pgsql_pcaps/pgsql_sync_separated_transactions.pcap")
        
        for i, data in enumerate(data_list):
            print(f"数据包 {i+1}: 长度 {len(data)} 字节")
            
            offset = 0
            msg_count = 0
            while offset < len(data):
                if offset + 5 > len(data):
                    break
                    
                is_valid, msg = validate_postgresql_message(data, offset)
                if is_valid:
                    msg_count += 1
                    print(f"  消息 {msg_count}: {msg}")
                    
                    # 检查CommandComplete消息的长度计算
                    if data[offset] == ord('C'):  # CommandComplete消息
                        msg_length = struct.unpack("!I", data[offset+1:offset+5])[0]
                        cmd_text = data[offset+5:offset+1+msg_length]
                        print(f"    CommandComplete内容: '{cmd_text.decode().rstrip(chr(0))}'")
                        print(f"    ✓ 动态长度计算: 声明长度={msg_length}, 实际内容长度={len(cmd_text)}")
                    
                    # 获取消息长度并移动到下一个消息
                    msg_length = struct.unpack("!I", data[offset+1:offset+5])[0]
                    offset += 1 + msg_length
                else:
                    print(f"  错误: {msg}")
                    break
            
            if msg_count > 0:
                print(f"  ✓ 成功解析 {msg_count} 个PostgreSQL消息")
            else:
                print(f"  ✗ 未找到有效的PostgreSQL消息")
            print()
            
    except Exception as e:
        print(f"验证失败: {e}")

def main():
    """主函数"""
    print("PostgreSQL协议修复验证工具")
    print("=" * 50)
    
    verify_large_result_scenario()
    verify_prepared_statement_scenario()
    verify_sync_separated_transactions_scenario()
    
    print("验证完成！")

if __name__ == "__main__":
    main()
