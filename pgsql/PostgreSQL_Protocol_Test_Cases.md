# PostgreSQL协议测试用例文档

## 概述

本文档详细描述了PostgreSQL协议测试用例的分类、功能和实现细节。所有测试用例都基于PostgreSQL官方协议文档实现，涵盖了协议的各个方面，包括认证、查询、事务、错误处理、流复制等核心功能。

## 测试用例分类

### 1. 认证类测试场景 (Authentication Test Cases)

#### 1.1 MD5密码认证场景
- **函数名**: `generate_auth_scenario()`
- **测试目的**: 验证标准MD5密码认证流程
- **协议消息**: StartupMessage → AuthenticationMD5Password → PasswordMessage → AuthenticationOk → ParameterStatus → BackendKeyData → ReadyForQuery
- **生成文件**: `pgsql_authentication.pcap`
- **测试特性**: 完整的认证握手流程，包含服务器参数状态同步

#### 1.2 明文密码认证场景
- **函数名**: `generate_cleartext_auth_scenario()`
- **测试目的**: 验证明文密码认证流程
- **协议消息**: StartupMessage → AuthenticationCleartextPassword → PasswordMessage → AuthenticationOk
- **生成文件**: `pgsql_cleartext_authentication.pcap`
- **测试特性**: 不安全的明文密码传输，用于测试协议兼容性

#### 1.3 SASL认证场景
- **函数名**: `generate_sasl_auth_scenario()`
- **测试目的**: 验证SCRAM-SHA-256 SASL认证流程
- **协议消息**: StartupMessage → AuthenticationSASL → SASLInitialResponse → AuthenticationSASLContinue → SASLResponse → AuthenticationSASLFinal → AuthenticationOk
- **生成文件**: `pgsql_sasl_authentication.pcap`
- **测试特性**: 现代安全认证机制，包含多轮挑战-响应

### 2. 查询类测试场景 (Query Test Cases)

#### 2.1 简单查询场景
- **函数名**: `generate_simple_query_scenario()`
- **测试目的**: 验证基本的简单查询协议
- **协议消息**: Query → RowDescription → DataRow → CommandComplete → ReadyForQuery
- **生成文件**: `pgsql_simple_query.pcap`
- **测试特性**: 最基础的查询-响应模式

#### 2.2 预处理语句场景
- **函数名**: `generate_prepared_statement_scenario()`
- **测试目的**: 验证扩展查询协议的预处理语句功能
- **协议消息**: Parse → Bind → Execute → Sync → ParseComplete → BindComplete → RowDescription → DataRow → CommandComplete → ReadyForQuery
- **生成文件**: `pgsql_prepared_statement.pcap`
- **测试特性**: 参数化查询，提高性能和安全性

#### 2.3 多查询场景
- **函数名**: `generate_multi_query_scenario()`
- **测试目的**: 验证单个Query消息包含多个SQL语句的处理
- **协议消息**: Query(多语句) → 多组(RowDescription → DataRow → CommandComplete) → ReadyForQuery
- **生成文件**: `pgsql_multi_query.pcap`
- **测试特性**: 批量查询执行

#### 2.4 二进制扩展查询场景
- **函数名**: `generate_binary_extended_query_scenario()`
- **测试目的**: 验证二进制格式数据传输
- **协议消息**: Parse → Describe → Bind(二进制格式) → Execute → Sync
- **生成文件**: `pgsql_binary_extended_query.pcap`
- **测试特性**: 高效的二进制数据传输格式

### 3. 事务类测试场景 (Transaction Test Cases)

#### 3.1 基本事务场景
- **函数名**: `generate_transaction_scenario()`
- **测试目的**: 验证基本的事务控制流程
- **协议消息**: Query(BEGIN) → Query(INSERT) → Query(COMMIT) → 对应的CommandComplete和ReadyForQuery
- **生成文件**: `pgsql_transaction.pcap`
- **测试特性**: 事务状态变化，ReadyForQuery状态指示器

#### 3.2 批量操作场景
- **函数名**: `generate_batch_operations_scenario()`
- **测试目的**: 验证事务中的批量操作
- **协议消息**: 事务内多个INSERT操作
- **生成文件**: `pgsql_batch_operations.pcap`
- **测试特性**: 事务一致性保证

### 4. COPY操作类测试场景 (COPY Operation Test Cases)

#### 4.1 COPY FROM STDIN场景
- **函数名**: `generate_auth_with_copy_scenario()`
- **测试目的**: 验证从客户端导入数据的COPY操作
- **协议消息**: Query(COPY FROM STDIN) → CopyInResponse → CopyData → CopyDone → CommandComplete
- **生成文件**: `pgsql_auth_with_copy.pcap`
- **测试特性**: 高效的批量数据导入

#### 4.2 COPY TO STDOUT场景
- **函数名**: `generate_copy_to_stdout_scenario()`
- **测试目的**: 验证向客户端导出数据的COPY操作
- **协议消息**: Query(COPY TO STDOUT) → CopyOutResponse → CopyData → CopyDone → CommandComplete
- **生成文件**: `pgsql_copy_to_stdout.pcap`
- **测试特性**: 高效的批量数据导出

#### 4.3 二进制COPY场景
- **函数名**: `generate_binary_copy_scenario()`
- **测试目的**: 验证二进制格式的COPY操作
- **协议消息**: 二进制格式的CopyInResponse和CopyData
- **生成文件**: `pgsql_binary_copy.pcap`
- **测试特性**: 高性能的二进制数据传输

### 5. 扩展查询协议类测试场景 (Extended Query Protocol Test Cases)

#### 5.1 Describe语句场景
- **函数名**: `generate_describe_statement_scenario()`
- **测试目的**: 验证Describe消息对预处理语句的描述功能
- **协议消息**: Parse → Describe(Statement) → Sync → ParseComplete → ParameterDescription → RowDescription → ReadyForQuery
- **生成文件**: `pgsql_describe_statement.pcap`
- **测试特性**: 元数据查询功能

#### 5.2 Describe门户场景
- **函数名**: `generate_describe_portal_scenario()`
- **测试目的**: 验证Describe消息对门户的描述功能
- **协议消息**: Parse → Bind → Describe(Portal) → Sync
- **生成文件**: `pgsql_describe_portal.pcap`
- **测试特性**: 门户元数据查询

#### 5.3 Close语句和门户场景
- **函数名**: `generate_close_statement_portal_scenario()`
- **测试目的**: 验证Close消息释放资源的功能
- **协议消息**: Parse → Bind → Close(Statement) → Close(Portal) → Sync
- **生成文件**: `pgsql_close_statement_portal.pcap`
- **测试特性**: 资源管理和清理

#### 5.4 Flush消息场景
- **函数名**: `generate_flush_scenario()`
- **测试目的**: 验证Flush消息的缓冲区刷新功能
- **协议消息**: Parse → Flush → Bind → Flush → Execute → Sync
- **生成文件**: `pgsql_flush.pcap`
- **测试特性**: 强制缓冲区刷新，确保消息及时发送

### 6. 数据类型类测试场景 (Data Type Test Cases)

#### 6.1 多种数据类型场景
- **函数名**: `generate_multiple_data_types_scenario()`
- **测试目的**: 验证PostgreSQL支持的各种数据类型
- **协议消息**: 包含int4, float4, float8, text, date, timestamp, json, uuid, bytea, array等类型的查询
- **生成文件**: `pgsql_multiple_data_types.pcap`
- **测试特性**: 全面的数据类型支持测试

#### 6.2 字符编码场景
- **函数名**: `generate_character_encoding_scenario()`
- **测试目的**: 验证字符编码参数变化的处理
- **协议消息**: SET client_encoding命令和ParameterStatus响应
- **生成文件**: `pgsql_character_encoding.pcap`
- **测试特性**: 动态字符编码切换

### 7. 管道化查询类测试场景 (Pipelined Query Test Cases)

#### 7.1 管道化查询场景
- **函数名**: `generate_pipelined_queries_scenario()`
- **测试目的**: 验证管道化查询的并发处理能力
- **协议消息**: 连续发送多个查询而不等待响应
- **生成文件**: `pgsql_pipelined_queries.pcap`
- **测试特性**: 提高网络利用率的并发查询

#### 7.2 Sync分隔事务场景
- **函数名**: `generate_sync_separated_transactions_scenario()`
- **测试目的**: 验证Sync消息分隔事务段的功能
- **协议消息**: 使用Sync消息分隔的多个事务段
- **生成文件**: `pgsql_sync_separated_transactions.pcap`
- **测试特性**: 事务边界控制

### 8. 函数调用协议类测试场景 (Function Call Protocol Test Cases)

#### 8.1 函数调用场景
- **函数名**: `generate_function_call_scenario()`
- **测试目的**: 验证PostgreSQL函数调用协议
- **协议消息**: FunctionCall → FunctionCallResponse → ReadyForQuery
- **生成文件**: `pgsql_function_call.pcap`
- **测试特性**: 直接函数调用，绕过SQL解析

### 9. 流复制协议类测试场景 (Streaming Replication Test Cases)

#### 9.1 START_REPLICATION场景
- **函数名**: `generate_start_replication_scenario()`
- **测试目的**: 验证流复制启动流程
- **协议消息**: START_REPLICATION → CopyBothResponse → WAL数据流
- **生成文件**: `pgsql_start_replication.pcap`
- **测试特性**: 高可用性复制机制

#### 9.2 WAL数据流场景
- **函数名**: `generate_wal_streaming_scenario()`
- **测试目的**: 验证WAL数据的连续传输
- **协议消息**: 连续的WAL数据包，包含各种操作类型
- **生成文件**: `pgsql_wal_streaming.pcap`
- **测试特性**: 实时数据同步

#### 9.3 复制反馈场景
- **函数名**: `generate_replication_feedback_scenario()`
- **测试目的**: 验证复制进度反馈机制
- **协议消息**: 复制反馈消息和状态同步
- **生成文件**: `pgsql_replication_feedback.pcap`
- **测试特性**: 复制状态监控

### 10. SSL/TLS连接类测试场景 (SSL/TLS Connection Test Cases)

#### 10.1 SSL支持场景
- **函数名**: `generate_ssl_request_supported_scenario()`
- **测试目的**: 验证服务器支持SSL连接的协商
- **协议消息**: SSLRequest → 'S'(支持SSL)
- **生成文件**: `pgsql_ssl_request_supported.pcap`
- **测试特性**: 安全连接协商

#### 10.2 SSL不支持场景
- **函数名**: `generate_ssl_request_not_supported_scenario()`
- **测试目的**: 验证服务器不支持SSL时的处理
- **协议消息**: SSLRequest → 'N'(不支持SSL)
- **生成文件**: `pgsql_ssl_request_not_supported.pcap`
- **测试特性**: 回退到非加密连接

### 11. 边界条件类测试场景 (Boundary Condition Test Cases)

#### 11.1 最大消息长度场景
- **函数名**: `generate_max_message_length_scenario()`
- **测试目的**: 验证协议对最大消息长度的处理
- **协议消息**: 接近最大长度限制的查询消息
- **生成文件**: `pgsql_max_message_length.pcap`
- **测试特性**: 边界值测试，缓冲区溢出防护

#### 11.2 最大参数数量场景
- **函数名**: `generate_max_parameters_scenario()`
- **测试目的**: 验证预处理语句的最大参数数量支持
- **协议消息**: 包含大量参数的Parse/Bind消息
- **生成文件**: `pgsql_max_parameters.pcap`
- **测试特性**: 参数数组边界处理

#### 11.3 最大字符串长度场景
- **函数名**: `generate_max_string_length_scenario()`
- **测试目的**: 验证最大字符串字段长度的处理
- **协议消息**: 包含超长字符串的查询和响应
- **生成文件**: `pgsql_max_string_length.pcap`
- **测试特性**: 字符串长度字段边界处理

#### 11.4 空数据处理场景
- **函数名**: `generate_empty_data_handling_scenario()`
- **测试目的**: 验证各种空值和零长度数据的处理
- **协议消息**: 包含NULL值、空字符串、零长度字段的查询
- **生成文件**: `pgsql_empty_data_handling.pcap`
- **测试特性**: 空数据的正确协议表示

## 异常/错误处理类测试场景 (Error Handling Test Cases)

### 12.1 认证失败类

#### 12.1.1 密码错误场景
- **函数名**: `generate_auth_failure_scenario()`
- **测试目的**: 验证错误密码导致的认证失败处理
- **协议消息**: StartupMessage → AuthenticationMD5Password → PasswordMessage(错误密码) → ErrorResponse(认证失败)
- **生成文件**: `pgsql_auth_failure.pcap`
- **错误类型**: FATAL级别认证错误

#### 12.1.2 用户不存在场景
- **函数名**: `generate_user_not_exist_scenario()`
- **测试目的**: 验证不存在用户的认证失败处理
- **协议消息**: StartupMessage(不存在用户) → ErrorResponse(用户不存在)
- **生成文件**: `pgsql_user_not_exist.pcap`
- **错误类型**: FATAL级别用户验证错误

### 12.2 查询错误类

#### 12.2.1 基本查询错误场景
- **函数名**: `generate_error_scenario()`
- **测试目的**: 验证基本的查询错误处理
- **协议消息**: Query(错误SQL) → ErrorResponse → ReadyForQuery
- **生成文件**: `pgsql_error.pcap`
- **错误类型**: 表不存在错误

#### 12.2.2 SQL语法错误场景
- **函数名**: `generate_syntax_error_scenario()`
- **测试目的**: 验证SQL语法错误的处理
- **协议消息**: Query(语法错误SQL) → ErrorResponse(语法错误)
- **生成文件**: `pgsql_syntax_error.pcap`
- **错误类型**: ERROR级别语法错误

#### 12.2.3 数据类型错误场景
- **函数名**: `generate_type_error_scenario()`
- **测试目的**: 验证数据类型不匹配错误的处理
- **协议消息**: Query(类型错误SQL) → ErrorResponse(类型错误)
- **生成文件**: `pgsql_type_error.pcap`
- **错误类型**: ERROR级别类型转换错误

#### 12.2.4 约束冲突错误场景
- **函数名**: `generate_constraint_violation_scenario()`
- **测试目的**: 验证数据库约束冲突错误的处理
- **协议消息**: Query(违反约束SQL) → ErrorResponse(约束冲突)
- **生成文件**: `pgsql_constraint_violation.pcap`
- **错误类型**: ERROR级别完整性约束违反

### 12.3 系统级错误类

#### 12.3.1 FATAL级别错误场景
- **函数名**: `generate_fatal_error_scenario()`
- **测试目的**: 验证FATAL级别错误的处理
- **协议消息**: Query(导致FATAL错误) → ErrorResponse(FATAL) → 连接关闭
- **生成文件**: `pgsql_fatal_error.pcap`
- **错误类型**: FATAL级别系统错误

#### 12.3.2 PANIC级别错误场景
- **函数名**: `generate_panic_error_scenario()`
- **测试目的**: 验证PANIC级别错误的处理
- **协议消息**: Query(导致PANIC错误) → ErrorResponse(PANIC) → 连接强制关闭
- **生成文件**: `pgsql_panic_error.pcap`
- **错误类型**: PANIC级别系统崩溃错误

### 12.4 操作错误类

#### 12.4.1 COPY操作失败场景
- **函数名**: `generate_copy_fail_scenario()`
- **测试目的**: 验证COPY操作中的错误处理
- **协议消息**: Query(COPY) → CopyInResponse → CopyFail → ErrorResponse
- **生成文件**: `pgsql_copy_fail.pcap`
- **错误类型**: COPY操作中断和错误恢复

#### 12.4.2 函数调用错误场景
- **函数名**: `generate_function_call_error_scenario()`
- **测试目的**: 验证函数调用协议中的错误处理
- **协议消息**: FunctionCall(无效函数) → ErrorResponse
- **生成文件**: `pgsql_function_call_error.pcap`
- **错误类型**: 函数不存在错误

#### 12.4.3 管道化查询错误处理场景
- **函数名**: `generate_pipelined_error_handling_scenario()`
- **测试目的**: 验证管道化查询中的错误处理和恢复
- **协议消息**: 管道化查询中的错误和后续查询的处理
- **生成文件**: `pgsql_pipelined_error_handling.pcap`
- **错误类型**: 管道中的错误隔离和恢复

### 12.5 其他场景

#### 12.5.1 查询取消场景
- **函数名**: `generate_cancel_query_scenario()`
- **测试目的**: 验证查询取消机制
- **协议消息**: Query(长时间查询) → CancelRequest → ErrorResponse(查询取消)
- **生成文件**: `pgsql_cancel_query.pcap`
- **特殊特性**: 使用独立连接发送取消请求

#### 12.5.2 通知响应场景
- **函数名**: `generate_notice_response_scenario()`
- **测试目的**: 验证NoticeResponse消息的处理
- **协议消息**: Query → NoticeResponse → 正常响应
- **生成文件**: `pgsql_notice_response.pcap`
- **特殊特性**: 非错误的通知消息

## 特殊功能场景

### 13.1 通知机制场景
- **函数名**: `generate_notification_scenario()`
- **测试目的**: 验证PostgreSQL的异步通知机制
- **协议消息**: LISTEN → NOTIFY → NotificationResponse
- **生成文件**: `pgsql_notification.pcap`
- **测试特性**: 异步消息推送

### 13.2 大结果集场景
- **函数名**: `generate_large_result_scenario()`
- **测试目的**: 验证大型结果集的处理能力
- **协议消息**: Query → RowDescription → 多个DataRow → CommandComplete
- **生成文件**: `pgsql_large_result.pcap`
- **测试特性**: 大数据量传输

### 13.3 多参数预处理语句场景
- **函数名**: `generate_multi_parameter_scenario()`
- **测试目的**: 验证多参数预处理语句的处理
- **协议消息**: Parse(多参数) → Bind(多参数值) → Execute
- **生成文件**: `pgsql_multi_parameter.pcap`
- **测试特性**: 复杂参数绑定

### 13.4 NoData响应场景
- **函数名**: `generate_nodata_scenario()`
- **测试目的**: 验证NoData响应的处理
- **协议消息**: Describe → NoData → ReadyForQuery
- **生成文件**: `pgsql_nodata.pcap`
- **测试特性**: 无数据返回的查询

### 13.5 高性能测试场景
- **函数名**: `generate_high_performance_scenario()`
- **测试目的**: 验证高并发高性能场景下的协议处理
- **协议消息**: 大量并发查询请求和响应
- **生成文件**: `pgsql_high_performance_100k_qps.pcap`
- **测试特性**: 100k QPS性能测试（默认注释，按需启用）

## 文件输出目录说明

- **正常测试场景**: 输出到 `pgsql_pcaps/` 目录
- **异常测试场景**: 输出到 `pgsql_abnormal_pcaps/` 目录

## 使用说明

1. 运行 `python3 generate_pgsql_pcap.py` 生成所有测试用例
2. 正常场景的pcap文件用于验证协议的正确实现
3. 异常场景的pcap文件用于测试错误处理和异常恢复机制
4. 每个pcap文件都包含完整的TCP握手和挥手过程
5. 所有消息都严格按照PostgreSQL官方协议文档实现

## 协议消息类型说明

本测试套件涵盖了以下PostgreSQL协议消息类型：

**客户端消息**: StartupMessage, Query, Parse, Bind, Execute, Describe, Close, Flush, Sync, PasswordMessage, SASLInitialResponse, SASLResponse, FunctionCall, CopyData, CopyDone, CopyFail, CancelRequest

**服务器消息**: AuthenticationOk, AuthenticationMD5Password, AuthenticationCleartextPassword, AuthenticationSASL, AuthenticationSASLContinue, AuthenticationSASLFinal, ParameterStatus, BackendKeyData, ReadyForQuery, RowDescription, DataRow, CommandComplete, ParseComplete, BindComplete, CloseComplete, NoData, ParameterDescription, ErrorResponse, NoticeResponse, NotificationResponse, CopyInResponse, CopyOutResponse, CopyBothResponse, FunctionCallResponse, EmptyQueryResponse

这些测试用例为PostgreSQL协议解析器的开发和验证提供了全面的测试覆盖。
