#!/usr/bin/env python3
"""
调试PostgreSQL DataRow消息格式的脚本
"""

import struct
import sys

def analyze_datarow_construction():
    """分析DataRow消息的构造过程"""
    print("=== DataRow消息构造分析 ===")
    
    # 模拟当前脚本中的DataRow构造
    data_row_content = struct.pack("!H", 3)  # 3列
    print(f"列数字段: {data_row_content.hex()} (长度: {len(data_row_content)})")
    
    # id值
    id_field = struct.pack("!I", 1) + b"1"
    data_row_content += id_field
    print(f"id字段: {id_field.hex()} (长度: {len(id_field)})")
    
    # name值
    name_field = struct.pack("!I", 5) + b"Alice"
    data_row_content += name_field
    print(f"name字段: {name_field.hex()} (长度: {len(name_field)})")
    
    # email值
    email_field = struct.pack("!I", 15) + b"<EMAIL>"
    data_row_content += email_field
    print(f"email字段: {email_field.hex()} (长度: {len(email_field)})")
    
    print(f"总内容长度: {len(data_row_content)}")
    print(f"内容十六进制: {data_row_content.hex()}")
    
    # 构造完整的DataRow消息
    data_row = b"D" + struct.pack("!I", len(data_row_content) + 4) + data_row_content
    print(f"完整DataRow消息长度: {len(data_row)}")
    print(f"完整DataRow消息: {data_row.hex()}")
    
    # 验证长度字段
    declared_length = struct.unpack("!I", data_row[1:5])[0]
    actual_content_length = len(data_row) - 1  # 减去消息类型字节
    print(f"声明长度: {declared_length}")
    print(f"实际内容长度: {actual_content_length}")
    print(f"长度匹配: {declared_length == actual_content_length}")
    
    return data_row

def analyze_command_complete():
    """分析CommandComplete消息的构造"""
    print("\n=== CommandComplete消息构造分析 ===")
    
    cmd_complete_text = b"SELECT 1\0"
    cmd_complete = b"C" + struct.pack("!I", len(cmd_complete_text) + 4) + cmd_complete_text
    
    print(f"CommandComplete文本: {cmd_complete_text}")
    print(f"文本长度: {len(cmd_complete_text)}")
    print(f"完整消息: {cmd_complete.hex()}")
    print(f"完整消息长度: {len(cmd_complete)}")
    
    # 验证长度字段
    declared_length = struct.unpack("!I", cmd_complete[1:5])[0]
    actual_content_length = len(cmd_complete) - 1
    print(f"声明长度: {declared_length}")
    print(f"实际内容长度: {actual_content_length}")
    print(f"长度匹配: {declared_length == actual_content_length}")
    
    return cmd_complete

def analyze_ready_for_query():
    """分析ReadyForQuery消息的构造"""
    print("\n=== ReadyForQuery消息构造分析 ===")
    
    ready = b"Z" + struct.pack("!IB", 5, 73)  # 73 = 'I' for idle
    
    print(f"ReadyForQuery消息: {ready.hex()}")
    print(f"消息长度: {len(ready)}")
    
    # 验证长度字段
    declared_length = struct.unpack("!I", ready[1:5])[0]
    actual_content_length = len(ready) - 1
    print(f"声明长度: {declared_length}")
    print(f"实际内容长度: {actual_content_length}")
    print(f"长度匹配: {declared_length == actual_content_length}")
    
    return ready

def analyze_combined_messages():
    """分析组合消息的字节流"""
    print("\n=== 组合消息分析 ===")
    
    # 构造RowDescription消息（简化版）
    row_desc = b"T" + struct.pack("!IH", 61, 3)  # 3列
    # id列
    row_desc += b"id\0" + struct.pack("!IHIHIHH", 0, 0, 23, 4, 4, 0, 0)
    # name列  
    row_desc += b"name\0" + struct.pack("!IHIHIHH", 0, 0, 25, 100, 100, 0, 0)
    # email列
    row_desc += b"email\0" + struct.pack("!IHIHIHH", 0, 0, 25, 100, 100, 0, 0)
    
    data_row = analyze_datarow_construction()
    cmd_complete = analyze_command_complete()
    ready = analyze_ready_for_query()
    
    # 组合所有消息
    combined = row_desc + data_row + cmd_complete + ready
    
    print(f"\n组合消息总长度: {len(combined)}")
    print(f"组合消息前100字节: {combined[:100].hex()}")
    print(f"组合消息后100字节: {combined[-100:].hex()}")
    
    # 逐个解析消息
    print("\n=== 逐个解析消息 ===")
    offset = 0
    msg_count = 0
    
    while offset < len(combined):
        if offset + 5 > len(combined):
            print(f"剩余数据不足5字节: {combined[offset:].hex()}")
            break
            
        msg_type = chr(combined[offset])
        msg_length = struct.unpack("!I", combined[offset+1:offset+5])[0]
        
        print(f"消息 {msg_count + 1}: 类型='{msg_type}', 长度={msg_length}")
        
        if offset + 1 + msg_length > len(combined):
            print(f"  错误: 声明长度超出剩余数据")
            print(f"  剩余数据: {combined[offset:].hex()}")
            break
            
        msg_data = combined[offset:offset + 1 + msg_length]
        print(f"  数据: {msg_data.hex()}")
        
        offset += 1 + msg_length
        msg_count += 1
        
        if msg_count > 10:  # 防止无限循环
            print("  警告: 消息数量过多，停止解析")
            break

if __name__ == "__main__":
    analyze_combined_messages()
