#!/usr/bin/env python3
"""
PostgreSQL PCAP文件验证工具
用于验证生成的PCAP文件的质量和正确性
"""

from scapy.all import *
import os
import sys

def analyze_pcap_file(filepath):
    """分析PCAP文件"""
    print(f"分析文件: {filepath}")
    print("-" * 50)
    
    if not os.path.exists(filepath):
        print(f"错误: 文件不存在 - {filepath}")
        return
    
    try:
        # 读取PCAP文件
        packets = rdpcap(filepath)
        total_packets = len(packets)
        
        # 文件大小
        file_size = os.path.getsize(filepath)
        file_size_mb = file_size / 1024 / 1024
        
        print(f"文件大小: {file_size_mb:.2f} MB")
        print(f"数据包总数: {total_packets:,}")
        
        # 分析数据包类型
        tcp_packets = 0
        pgsql_packets = 0
        handshake_packets = 0
        data_packets = 0
        
        packet_sizes = []
        pgsql_sizes = []
        
        for pkt in packets:
            packet_sizes.append(len(pkt))
            
            if TCP in pkt:
                tcp_packets += 1
                tcp_layer = pkt[TCP]
                
                # 检查TCP标志
                if tcp_layer.flags == 2:  # SYN
                    handshake_packets += 1
                elif tcp_layer.flags == 18:  # SYN-ACK
                    handshake_packets += 1
                elif tcp_layer.flags == 16:  # ACK
                    if len(pkt[TCP].payload) == 0:
                        handshake_packets += 1
                
                # 检查是否包含PostgreSQL数据
                if len(pkt[TCP].payload) > 0:
                    data_packets += 1
                    payload = bytes(pkt[TCP].payload)
                    
                    # 简单检查PostgreSQL协议特征
                    if (payload.startswith(b'Q') or  # Query
                        payload.startswith(b'T') or  # RowDescription
                        payload.startswith(b'D') or  # DataRow
                        payload.startswith(b'C') or  # CommandComplete
                        payload.startswith(b'Z')):   # ReadyForQuery
                        pgsql_packets += 1
                        pgsql_sizes.append(len(pkt))
        
        # 统计信息
        print(f"TCP数据包: {tcp_packets}")
        print(f"握手相关包: {handshake_packets}")
        print(f"数据包: {data_packets}")
        print(f"PostgreSQL协议包: {pgsql_packets}")
        
        # 包大小统计
        if packet_sizes:
            avg_size = sum(packet_sizes) / len(packet_sizes)
            min_size = min(packet_sizes)
            max_size = max(packet_sizes)
            print(f"平均包大小: {avg_size:.1f} 字节")
            print(f"最小包大小: {min_size} 字节")
            print(f"最大包大小: {max_size} 字节")
        
        if pgsql_sizes:
            avg_pgsql_size = sum(pgsql_sizes) / len(pgsql_sizes)
            print(f"PostgreSQL包平均大小: {avg_pgsql_size:.1f} 字节")
        
        # 估算QPS
        if pgsql_packets > 0:
            # 假设一半是请求，一半是响应
            estimated_qps = pgsql_packets // 2
            print(f"估算QPS: {estimated_qps:,}")
            
            # 估算带宽需求
            if avg_pgsql_size:
                bandwidth_bps = estimated_qps * avg_pgsql_size * 8
                bandwidth_mbps = bandwidth_bps / 1024 / 1024
                print(f"估算带宽需求: {bandwidth_mbps:.0f} Mbps")
        
        print("-" * 50)
        print("验证结果:")
        
        # 验证检查
        checks_passed = 0
        total_checks = 5
        
        # 检查1: 文件不为空
        if total_packets > 0:
            print("✓ 文件包含数据包")
            checks_passed += 1
        else:
            print("✗ 文件为空或无效")
        
        # 检查2: 包含TCP数据包
        if tcp_packets > 0:
            print("✓ 包含TCP数据包")
            checks_passed += 1
        else:
            print("✗ 未找到TCP数据包")
        
        # 检查3: 包含PostgreSQL协议数据
        if pgsql_packets > 0:
            print("✓ 包含PostgreSQL协议数据")
            checks_passed += 1
        else:
            print("✗ 未找到PostgreSQL协议数据")
        
        # 检查4: 包大小合理
        if pgsql_sizes and 100 <= avg_pgsql_size <= 2000:
            print("✓ PostgreSQL包大小合理")
            checks_passed += 1
        else:
            print("✗ PostgreSQL包大小异常")
        
        # 检查5: 包含握手数据
        if handshake_packets >= 3:
            print("✓ 包含TCP握手")
            checks_passed += 1
        else:
            print("✗ TCP握手不完整")
        
        print(f"\n验证通过: {checks_passed}/{total_checks}")
        
        if checks_passed == total_checks:
            print("🎉 文件验证通过，可用于测试！")
        elif checks_passed >= 3:
            print("⚠️  文件基本可用，但存在一些问题")
        else:
            print("❌ 文件存在严重问题，不建议使用")
            
    except Exception as e:
        print(f"分析出错: {e}")

def main():
    """主函数"""
    print("PostgreSQL PCAP文件验证工具")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        # 命令行参数指定文件
        filepath = sys.argv[1]
        analyze_pcap_file(filepath)
    else:
        # 交互式选择文件
        pcap_dir = "pgsql_pcaps"
        if not os.path.exists(pcap_dir):
            print(f"错误: 目录不存在 - {pcap_dir}")
            print("请先运行生成脚本创建PCAP文件")
            return
        
        # 列出所有PCAP文件
        pcap_files = [f for f in os.listdir(pcap_dir) if f.endswith('.pcap')]
        
        if not pcap_files:
            print(f"在 {pcap_dir} 目录中未找到PCAP文件")
            return
        
        print("可用的PCAP文件:")
        for i, filename in enumerate(pcap_files, 1):
            filepath = os.path.join(pcap_dir, filename)
            file_size = os.path.getsize(filepath) / 1024 / 1024
            print(f"{i}. {filename} ({file_size:.1f} MB)")
        
        try:
            choice = input(f"\n请选择要验证的文件 (1-{len(pcap_files)}): ").strip()
            if choice:
                index = int(choice) - 1
                if 0 <= index < len(pcap_files):
                    filepath = os.path.join(pcap_dir, pcap_files[index])
                    print()
                    analyze_pcap_file(filepath)
                else:
                    print("无效的选择")
            else:
                print("已取消")
        except ValueError:
            print("无效的输入")

if __name__ == "__main__":
    main() 